import { HeroImageLayoutProps } from './types';
import * as SC from './styles';

export default function HeroImageLayout({ children }: HeroImageLayoutProps) {
  return (
    <SC.Container>
      <SC.Content>
        {children}
        <SC.ImageBox>
          <img
            src="/svgs/undraw_file_sync_ot38.svg"
            alt="Setas saindo de um notebook e apontando para um tablet e um celular"
            loading="lazy"
          />
        </SC.ImageBox>
      </SC.Content>
    </SC.Container>
  );
}
