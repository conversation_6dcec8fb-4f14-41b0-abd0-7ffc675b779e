import styled from 'styled-components';
import { InputText } from '@onyma-ds/react';
import Icons from '../Icons';

export const Container = styled.div`
  position: relative;
`;

export const Input = styled(InputText)``;

export const Icon = styled(Icons.Symbol).attrs({
  type: 'rounded',
  size: 20,
})`
  color: ${({ theme }) => theme.colors.gray_60};

  ${Input} + & {
    position: absolute;
    top: 50%;
    right: 0;
    transform: translate(
      calc(${({ theme }) => theme.spacings.xxxs} * -1),
      -50%
    );
    cursor: pointer;
  }
`;
