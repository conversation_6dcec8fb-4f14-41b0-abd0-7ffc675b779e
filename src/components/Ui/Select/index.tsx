import { Label } from '@/pages/Login/components/LoginForm/styles';
import { Combobox, Text } from '@onyma-ds/react';
import { useState } from 'react';
import { CompanyCheckboxContainer, TriggerSpan } from './styles';
import { LabelAndValue } from '@/@types/LabelAndValue';

interface ComboboxCompanyProps {
  label: string;
  selectedItem?: LabelAndValue;
  options: LabelAndValue[];
  handleSelect: (option: LabelAndValue) => void;
}

export function Select({
  label,
  selectedItem,
  options,
  handleSelect,
}: ComboboxCompanyProps) {
  const [open, setOpen] = useState(false);
  return (
    <div>
      <Label>{label}</Label>
      <Combobox.Root
        modal
        open={open}
        onOpenChange={setOpen}
      >
        <Combobox.Trigger
          placeholder=""
          data-placeholder={!selectedItem}
        >
          <TriggerSpan>
            {selectedItem?.label ? selectedItem.label : 'Selecione...'}
          </TriggerSpan>
        </Combobox.Trigger>
        <Combobox.Portal>
          <Combobox.Content placeholder="">
            <Combobox.Command>
              <Combobox.List>
                <Combobox.Empty>
                  <Text>Nenhum resultado encontrado</Text>
                </Combobox.Empty>
                <Combobox.Viewport>
                  <Combobox.Group>
                    {options.map((option) => (
                      <CompanyCheckboxContainer key={option.value}>
                        <Combobox.Item
                          placeholder=""
                          value={option.label}
                          onSelect={() => handleSelect(option)}
                        >
                          <span>{option.label}</span>
                        </Combobox.Item>
                      </CompanyCheckboxContainer>
                    ))}
                  </Combobox.Group>
                </Combobox.Viewport>
              </Combobox.List>
            </Combobox.Command>
          </Combobox.Content>
        </Combobox.Portal>
      </Combobox.Root>
    </div>
  );
}
