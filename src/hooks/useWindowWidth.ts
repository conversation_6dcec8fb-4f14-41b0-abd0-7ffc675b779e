import { useEffect, useMemo, useState } from 'react';
import { breakpoints } from '@/utils/constants';

export const useWindowWidth = () => {
  const [screenWidth, setScreenWidth] = useState(0);

  const handleResize = () => {
    setScreenWidth(window.innerWidth);
  };

  useEffect(() => {
    handleResize();
    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const isMobile = useMemo(() => {
    return screenWidth < breakpoints.md;
  }, [screenWidth]);

  return {
    screenWidth,
    isMobile,
  };
};
