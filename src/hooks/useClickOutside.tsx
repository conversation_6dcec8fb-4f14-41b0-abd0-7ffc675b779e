/* eslint-disable react-hooks/exhaustive-deps */
import { RefObject, useEffect } from 'react';

export const useClickOutside = (
  ref: RefObject<HTMLElement>,
  callback?: CallableFunction,
) => {
  const handleClickOutside = (event: globalThis.MouseEvent) => {
    if (ref.current && !ref.current.contains(event.target as Node)) {
      callback && callback();
    }
  };

  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [ref]);
};
