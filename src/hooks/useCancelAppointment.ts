import { useMutation, useQueryClient } from '@tanstack/react-query';
import { remoteLoadAppointments } from '@/services/api/calendar/remoteLoadAppointments';
import { useApi } from '@/contexts/api';

type AppointmentsQuery = Awaited<ReturnType<typeof remoteLoadAppointments>>;

export const useCancelAppointment = () => {
  const {
    calendar: { cancelAppointment },
  } = useApi();
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ['cancelAppointment'],
    mutationFn: cancelAppointment,
    onMutate: async (canceledAppointment) => {
      await queryClient.cancelQueries({ queryKey: ['appointments'] });

      const previousAppointments = queryClient.getQueryData<AppointmentsQuery>([
        'appointments',
      ]);

      queryClient.setQueryData<AppointmentsQuery>(['appointments'], (prev) => {
        if (!prev) return prev;
        return {
          ...prev,
          result: prev.result.filter(
            (appointment) =>
              appointment.codigoAgendamento !==
              canceledAppointment.codigoAgendamento,
          ),
        };
      });

      return {
        previousAppointments,
      };
    },
    onError: (_error, _variables, context) => {
      queryClient.setQueryData(['appointments'], context?.previousAppointments);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ['appointments'] });
    },
  });
};
