/* eslint-disable react-hooks/exhaustive-deps */
import { ClientCompany } from '@/services/api/clients/remoteLoadClientCompany';
import {
  PropsWithChildren,
  createContext,
  useCallback,
  useEffect,
  useState,
} from 'react';
import { useApi } from '../api';
import { useAuth } from '../auth';
import { CompanyContextData } from './types';
import { useToast } from '@onyma-ds/react';

export const CompanyContext = createContext<CompanyContextData | null>(null);

export const CompanyContextProvider = ({ children }: PropsWithChildren) => {
  const {
    clients: { loadClientCompany },
  } = useApi();
  const { user } = useAuth();
  const { addToast } = useToast();
  const [company, setCompany] = useState<ClientCompany | null>(null);

  const getCurrentCompany = useCallback(async () => {
    if (user?.companyId) {
      try {
        const response = await loadClientCompany(user.companyId as string);
        setCompany(response.result);
      } catch (error) {
        setCompany(null);
        addToast({
          type: 'error',
          title: 'Erro ao buscar empresa.',
          description: 'Não foi possível buscar a empresa.',
          timeout: 5000,
        });
      }
    }
  }, [user]);

  useEffect(() => {
    getCurrentCompany();
  }, [getCurrentCompany, user]);

  useEffect(() => {
    getCurrentCompany();
  }, []);

  const setCompanyData = (value: ClientCompany | null) => {
    setCompany(value);
  };

  return (
    <CompanyContext.Provider
      value={{
        company: company as ClientCompany,
        setCompanyData,
      }}
    >
      {children}
    </CompanyContext.Provider>
  );
};
