import { AuthRole } from '@/@types/AuthRole';
import { remoteLogin } from '@/services/api/auth/remoteLogin';

export type AuthUser = {
  uuid: string;
  fullName: string;
  email: string;
  companyId?: string | null;
  companyName?: string | null;
  currentCompany?: {
    id: string;
    nome: string;
  };
  companies: {
    id: string;
    nome: string;
  }[];
  currentRole: AuthRole;
  socRegister: string;
  socCode: string;
  isFirstLogin: boolean;
  roles: AuthRole[];
};

export type AuthContextData = {
  user: AuthUser;
  login: LoginFunction;
  logout: () => void;
  setUserData: (data: AuthUser) => void;
};

export type LoginFunction = (
  params: Parameters<typeof remoteLogin>[number] & {
    keepSession: boolean;
  },
) => ReturnType<typeof remoteLogin>;
