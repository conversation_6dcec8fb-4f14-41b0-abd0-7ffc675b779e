import { remoteChangePassword } from '@/services/api/auth/remoteChangePassword';
import { remoteChangePasswordWithToken } from '@/services/api/auth/remoteChangePasswordWithToken';
import { remoteLoadProfile } from '@/services/api/auth/remoteLoadProfile';
import { remoteRequestAccess } from '@/services/api/auth/remoteRequestAccess';
import { remoteSendResetPasswordLink } from '@/services/api/auth/remoteSendResetPasswordLink';
import { remoteLoadBIPage } from '@/services/api/bi/remoteLoadBIPage';
import { remoteLoadBIPages } from '@/services/api/bi/remoteLoadBIPages';
import { remoteLoadBIReport } from '@/services/api/bi/remoteLoadBIReport';
import { remoteLoadBIReports } from '@/services/api/bi/remoteLoadBIReports';
import { remoteLoadPowerBIParams } from '@/services/api/bi/remoteLoadPowerBIParams';
import { remoteCancelAppointment } from '@/services/api/calendar/remoteCancelAppointment';
import { remoteCreateAgenda } from '@/services/api/calendar/remoteCreateAgenda';
import { remoteDeleteAppointment } from '@/services/api/calendar/remoteDeleteCalendar';
import { remoteEditAgenda } from '@/services/api/calendar/remoteEditAgenda';
import { remoteLoadAppointments } from '@/services/api/calendar/remoteLoadAppointments';
import { remoteLoadAvailableDates } from '@/services/api/calendar/remoteLoadAvailableDates';
import { remoteLoadCalendar } from '@/services/api/calendar/remoteLoadCalendar';
import { remoteLoadCalendars } from '@/services/api/calendar/remoteLoadCalendars';
import { remoteLoadCompromissos } from '@/services/api/calendar/remoteLoadCompromissos';
import { remoteLoadExamsType } from '@/services/api/calendar/remoteLoadExamsType';
import { remoteManualCreateSchedule } from '@/services/api/calendar/remoteManualCreateSchedule';
import { remoteSOCCreateSchedule } from '@/services/api/calendar/remoteSOCCreateSchedule';
import { remoteCreateClientCompany } from '@/services/api/clients/remoteCreateClientCompany';
import { remoteLoadClientCompanies } from '@/services/api/clients/remoteLoadClientCompanies';
import { remoteLoadClientCompany } from '@/services/api/clients/remoteLoadClientCompany';
import { remoteUpdateClientCompany } from '@/services/api/clients/remoteUpdateClientCompany';
import { remoteUpdateUserRole } from '@/services/api/clients/remoteUpdateUserRole';
import { remoteLoadPowerBIApplicationId } from '@/services/api/configs/remoteLoadPowerBIApplicationId';
import { remoteLoadPowerBiWorkspaceId } from '@/services/api/configs/remoteLoadPowerBIGroupId';
import { remoteLoadPowerBITenant } from '@/services/api/configs/remoteLoadPowerBITenant';
import { remoteCreateFile } from '@/services/api/file/remoteCreateFile';
import { remoteDeleteFile } from '@/services/api/file/remoteDeleteFile';
import { remoteEditFile } from '@/services/api/file/remoteEditFile';
import { remoteGetAllFiles } from '@/services/api/file/remoteGetAllFiles';
import { remoteGetAllFileTypeById } from '@/services/api/file/remoteGetFileTypeById';
import { remoteGetAllFileTypes } from '@/services/api/file/remoteGetFileTypes';
import { remoteCreateMenu } from '@/services/api/menus/remoteCreateMenu';
import { remoteLoadMenu } from '@/services/api/menus/remoteLoadMenu';
import { remoteLoadMenus } from '@/services/api/menus/remoteLoadMenus';
import { remoteUpdateMenu } from '@/services/api/menus/remoteUpdateMenu';
import { remoteCreateUserGroup } from '@/services/api/userGroup/remoteCreateNewUserGroup';
import { remoteEditUserGroup } from '@/services/api/userGroup/remoteEditNewUserGroup';
import { remoteLoadUserGroups } from '@/services/api/userGroup/remoteLoadUserGroup/remoteLoadUserGroup';
import { remoteCreateUser } from '@/services/api/users/remoteCreateUser';
import { remoteLoadProfiles } from '@/services/api/users/remoteLoadProfiles';
import { remoteLoadUser } from '@/services/api/users/remoteLoadUser';
import { remoteLoadUsers } from '@/services/api/users/remoteLoadUsers';
import { remoteUpdateUser } from '@/services/api/users/remoteUpdateUser';
import { remoteSendPsicossocialFile } from '@/services/import/remoteImportCollaborator';
import { PropsWithChildren, createContext } from 'react';
import { ApiContextData } from './types';
import { remoteGetForms } from '@/services/api/forms/remoteGetForms';

export const ApiContext = createContext<ApiContextData | null>(null);

const apis: ApiContextData = {
  auth: {
    requestAccess: remoteRequestAccess,
    loadProfile: remoteLoadProfile,
    changePassword: remoteChangePassword,
    changePasswordWithToken: remoteChangePasswordWithToken,
    sendResetPasswordLink: remoteSendResetPasswordLink,
  },
  clients: {
    loadClientCompanies: remoteLoadClientCompanies,
    loadClientCompany: remoteLoadClientCompany,
    createClientCompany: remoteCreateClientCompany,
    updateClientCompany: remoteUpdateClientCompany,
    updateUserRole: remoteUpdateUserRole,
  },
  menus: {
    loadMenus: remoteLoadMenus,
    loadMenu: remoteLoadMenu,
    createMenu: remoteCreateMenu,
    updateMenu: remoteUpdateMenu,
  },
  users: {
    loadUsers: remoteLoadUsers,
    loadUser: remoteLoadUser,
    createUser: remoteCreateUser,
    updateUser: remoteUpdateUser,
    loadProfiles: remoteLoadProfiles,
  },
  import: {
    loadSendPsicossocialFile: remoteSendPsicossocialFile,
  },
  bi: {
    loadBIPage: remoteLoadBIPage,
    loadBIPages: remoteLoadBIPages,
    loadBIReport: remoteLoadBIReport,
    loadBIReports: remoteLoadBIReports,
    loadPowerBIParams: remoteLoadPowerBIParams,
  },
  configs: {
    loadPowerBIApplicationId: remoteLoadPowerBIApplicationId,
    loadPowerBiWorkspaceId: remoteLoadPowerBiWorkspaceId,
    loadPowerBiTenant: remoteLoadPowerBITenant,
  },
  calendar: {
    loadCalendar: remoteLoadCalendar,
    loadCalendars: remoteLoadCalendars,
    loadAvailableDates: remoteLoadAvailableDates,
    loadAppointments: remoteLoadAppointments,
    cancelAppointment: remoteCancelAppointment,
    socCreateSchedule: remoteSOCCreateSchedule,
    manualCreateSchedule: remoteManualCreateSchedule,
    remoteDeleteAppointment: remoteDeleteAppointment,
    remoteCreateAgenda: remoteCreateAgenda,
    remoteEditAgenda: remoteEditAgenda,
    remoteLoadCompromissos: remoteLoadCompromissos,
    remoteLoadExamsType: remoteLoadExamsType,
  },
  userGroup: {
    loadUserGroups: remoteLoadUserGroups,
    createUserGroup: remoteCreateUserGroup,
    editUserGroup: remoteEditUserGroup,
  },
  forms: {
    loadForms: remoteGetForms,
  },
  file: {
    loadFileTypes: remoteGetAllFileTypes,
    loadFiles: remoteGetAllFiles,
    remoteCreateFile: remoteCreateFile,
    remoteEditFile: remoteEditFile,
    remoteDeleteFile: remoteDeleteFile,
    remoteGetFileTypeById: remoteGetAllFileTypeById,
  },
};

export const ApiContextProvider = ({ children }: PropsWithChildren) => {
  return <ApiContext.Provider value={apis}>{children}</ApiContext.Provider>;
};
