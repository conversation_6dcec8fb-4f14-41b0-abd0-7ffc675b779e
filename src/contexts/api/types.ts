import { RemoteChangePassword } from '@/services/api/auth/remoteChangePassword';
import { RemoteChangePasswordWithToken } from '@/services/api/auth/remoteChangePasswordWithToken';
import { RemoteLoadProfile } from '@/services/api/auth/remoteLoadProfile';
import { RemoteRequestAccess } from '@/services/api/auth/remoteRequestAccess/types';
import { RemoteSendResetPasswordLink } from '@/services/api/auth/remoteSendResetPasswordLink';
import { RemoteLoadBIPage } from '@/services/api/bi/remoteLoadBIPage';
import { RemoteLoadBIPages } from '@/services/api/bi/remoteLoadBIPages';
import { RemoteLoadBIReport } from '@/services/api/bi/remoteLoadBIReport';
import { RemoteLoadBIReports } from '@/services/api/bi/remoteLoadBIReports';
import { RemoteLoadPowerBIParams } from '@/services/api/bi/remoteLoadPowerBIParams';
import { RemoteCancelAppointment } from '@/services/api/calendar/remoteCancelAppointment';
import { RemoteCreateAgenda } from '@/services/api/calendar/remoteCreateAgenda';
import { RemoteDeleteAppointment } from '@/services/api/calendar/remoteDeleteCalendar';
import { RemoteEditAgenda } from '@/services/api/calendar/remoteEditAgenda';
import { RemoteLoadAppointments } from '@/services/api/calendar/remoteLoadAppointments';
import { RemoteLoadAvailableDates } from '@/services/api/calendar/remoteLoadAvailableDates';
import { RemoteLoadCalendar } from '@/services/api/calendar/remoteLoadCalendar';
import { RemoteLoadCalendars } from '@/services/api/calendar/remoteLoadCalendars';
import { RemoteLoadCompromissos } from '@/services/api/calendar/remoteLoadCompromissos';
import { RemoteLoadExamsType } from '@/services/api/calendar/remoteLoadExamsType';
import { RemoteManualCreateSchedule } from '@/services/api/calendar/remoteManualCreateSchedule';
import { RemoteSOCCreateSchedule } from '@/services/api/calendar/remoteSOCCreateSchedule';
import { RemoteCreateClientCompany } from '@/services/api/clients/remoteCreateClientCompany';
import { RemoteLoadClientCompanies } from '@/services/api/clients/remoteLoadClientCompanies';
import { RemoteLoadClientCompany } from '@/services/api/clients/remoteLoadClientCompany';
import { RemoteUpdateClientCompany } from '@/services/api/clients/remoteUpdateClientCompany';
import { RemoteUpdateUserRole } from '@/services/api/clients/remoteUpdateUserRole';
import { RemoteLoadPowerBIApplicationId } from '@/services/api/configs/remoteLoadPowerBIApplicationId';
import { RemoteLoadPowerBiWorkspaceId } from '@/services/api/configs/remoteLoadPowerBIGroupId';
import { RemoteLoadPowerBITenant } from '@/services/api/configs/remoteLoadPowerBITenant';
import { remoteCreateFileType } from '@/services/api/file/remoteCreateFile';
import { remoteDeleteFileType } from '@/services/api/file/remoteDeleteFile';
import { IremoteEditFile } from '@/services/api/file/remoteEditFile';
import { remoteFetchAllFile } from '@/services/api/file/remoteGetAllFiles';
import { remoteFetchAllFileTypeById } from '@/services/api/file/remoteGetFileTypeById';
import { remoteFetchAllFileTypes } from '@/services/api/file/remoteGetFileTypes';
import { RemoteGetFormsType } from '@/services/api/forms/remoteGetForms';
import { RemoteCreateMenu } from '@/services/api/menus/remoteCreateMenu';
import { RemoteLoadMenu } from '@/services/api/menus/remoteLoadMenu';
import { RemoteLoadMenus } from '@/services/api/menus/remoteLoadMenus';
import { RemoteUpdateMenu } from '@/services/api/menus/remoteUpdateMenu';
import { RemoteCreateUserGroup } from '@/services/api/userGroup/remoteCreateNewUserGroup';
import { RemoteEditUserGroup } from '@/services/api/userGroup/remoteEditNewUserGroup';
import { RemoteLoadUserGroup } from '@/services/api/userGroup/remoteLoadUserGroup/types';
import { RemoteCreateUser } from '@/services/api/users/remoteCreateUser';
import { RemoteLoadProfiles } from '@/services/api/users/remoteLoadProfiles';
import { RemoteLoadUser } from '@/services/api/users/remoteLoadUser';
import { RemoteLoadUsers } from '@/services/api/users/remoteLoadUsers';
import { RemoteUpdateUser } from '@/services/api/users/remoteUpdateUser';
import { RemoteImportCollaborator } from '@/services/import/remoteImportCollaborator';

export type ApiContextData = {
  auth: {
    requestAccess: RemoteRequestAccess;
    changePassword: RemoteChangePassword;
    changePasswordWithToken: RemoteChangePasswordWithToken;
    sendResetPasswordLink: RemoteSendResetPasswordLink;
    loadProfile: RemoteLoadProfile;
  };
  clients: {
    loadClientCompanies: RemoteLoadClientCompanies;
    loadClientCompany: RemoteLoadClientCompany;
    createClientCompany: RemoteCreateClientCompany;
    updateClientCompany: RemoteUpdateClientCompany;
    updateUserRole: RemoteUpdateUserRole;
  };
  bi: {
    loadBIPage: RemoteLoadBIPage;
    loadBIPages: RemoteLoadBIPages;
    loadBIReport: RemoteLoadBIReport;
    loadBIReports: RemoteLoadBIReports;
    loadPowerBIParams: RemoteLoadPowerBIParams;
  };
  menus: {
    loadMenus: RemoteLoadMenus;
    loadMenu: RemoteLoadMenu;
    createMenu: RemoteCreateMenu;
    updateMenu: RemoteUpdateMenu;
  };
  users: {
    loadUsers: RemoteLoadUsers;
    loadUser: RemoteLoadUser;
    createUser: RemoteCreateUser;
    updateUser: RemoteUpdateUser;
    loadProfiles: RemoteLoadProfiles;
  };
  import: {
    loadSendPsicossocialFile: RemoteImportCollaborator;
  };
  forms: {
    loadForms: RemoteGetFormsType;
  };
  configs: {
    loadPowerBIApplicationId: RemoteLoadPowerBIApplicationId;
    loadPowerBiWorkspaceId: RemoteLoadPowerBiWorkspaceId;
    loadPowerBiTenant: RemoteLoadPowerBITenant;
  };
  calendar: {
    loadCalendars: RemoteLoadCalendars;
    loadAvailableDates: RemoteLoadAvailableDates;
    loadAppointments: RemoteLoadAppointments;
    cancelAppointment: RemoteCancelAppointment;
    socCreateSchedule: RemoteSOCCreateSchedule;
    manualCreateSchedule: RemoteManualCreateSchedule;
    loadCalendar: RemoteLoadCalendar;
    remoteDeleteAppointment: RemoteDeleteAppointment;
    remoteCreateAgenda: RemoteCreateAgenda;
    remoteEditAgenda: RemoteEditAgenda;
    remoteLoadCompromissos: RemoteLoadCompromissos;
    remoteLoadExamsType: RemoteLoadExamsType;
  };
  userGroup: {
    loadUserGroups: RemoteLoadUserGroup;
    createUserGroup: RemoteCreateUserGroup;
    editUserGroup: RemoteEditUserGroup;
  };
  file: {
    loadFileTypes: remoteFetchAllFileTypes;
    loadFiles: remoteFetchAllFile;
    remoteCreateFile: remoteCreateFileType;
    remoteEditFile: IremoteEditFile;
    remoteDeleteFile: remoteDeleteFileType;
    remoteGetFileTypeById: remoteFetchAllFileTypeById;
  };
};
