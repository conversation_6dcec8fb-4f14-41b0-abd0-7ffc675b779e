import { z } from 'zod';

export const requestAccessSchema = z.object({
  name: z
    .string({ required_error: 'Esse campo é obrigatório' })
    .min(1, 'Esse campo é obrigatório')
    .refine((value) => value.trim().split(' ').length >= 2, {
      message: 'É necessário o preenchimento do nome e sobrenome',
    })
    .refine((value) => /^[a-zA-Z\s]+$/.test(value), {
      message: 'Campo inválido',
    }),
  company: z
    .string({ required_error: 'Esse campo é obrigatório' })
    .min(1, 'Esse campo é obrigatório'),
  email: z
    .string({ required_error: 'Esse campo é obrigatório' })
    .min(1, 'Esse campo é obrigatório')
    .email('Campo inválido'),
  role: z
    .string({ required_error: 'Esse campo é obrigatório' })
    .min(1, 'Esse campo é obrigatório'),
});

export type RequestAccessFormType = z.infer<typeof requestAccessSchema>;
