import { Navigate } from 'react-router-dom';
import { useAuth } from '@/contexts/auth';
import { Main } from './components';

export default function FirstAccessPage() {
  const { user } = useAuth();

  if (!user) {
    return (
      <Navigate
        to="/logout"
        replace
      />
    );
  }

  if (!user.isFirstLogin) {
    return (
      <Navigate
        to="/app"
        replace
      />
    );
  }

  return <Main />;
}
