import styled from 'styled-components';
import { Button, Heading } from '@onyma-ds/react';
import { Icons } from '@/components';

export const Main = styled.main`
  width: 100%;
  max-width: 23rem;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacings.lg};
`;

export const Header = styled.header`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: ${({ theme }) => theme.spacings.xxxs};
`;

export const RoundedIcon = styled(Icons.Symbol).attrs({
  type: 'rounded',
  size: 48,
})`
  color: ${({ theme }) => theme.colors.success};
  background-color: color-mix(
    in srgb,
    ${({ theme }) => theme.colors.success} 8%,
    transparent
  );
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  padding: ${({ theme }) => theme.spacings.xxxs};
`;

export const SuccessHeading = styled(Heading).attrs({
  type: 'heading_03',
})`
  font-weight: 600;
  text-align: center;
`;

export const AccessMyAccountButton = styled(Button).attrs({
  variant: 'secondary',
  color: 'white',
})`
  font-weight: 600;
  width: 100%;
`;
