import { Link } from 'react-router-dom';
import * as SC from './styles';

export default function PasswordChangedSuccessfully() {
  return (
    <SC.Main>
      <SC.Header>
        <SC.RoundedIcon name="check_circle" />
        <SC.SuccessHeading>Senha alterada com sucesso!</SC.SuccessHeading>
      </SC.Header>
      <Link to="/app/home">
        <SC.AccessMyAccountButton type="button">
          Acessar minha conta
        </SC.AccessMyAccountButton>
      </Link>
    </SC.Main>
  );
}
