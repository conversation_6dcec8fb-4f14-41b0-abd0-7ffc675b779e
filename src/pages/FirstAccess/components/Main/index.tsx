import { useState } from 'react';
import { HeroImageLayout } from '@/components';
import { ChangeDefaultPasswordForm, PasswordChangedSuccessfully } from '..';

export default function Main() {
  const [isSuccessful, setIsSuccessful] = useState(false);

  const renderContent = () => {
    if (isSuccessful) return <PasswordChangedSuccessfully />;
    return (
      <ChangeDefaultPasswordForm onSuccess={() => setIsSuccessful(true)} />
    );
  };

  return <HeroImageLayout>{renderContent()}</HeroImageLayout>;
}
