import { z } from 'zod';

export const changeDefaultPasswordFormSchema = z
  .object({
    password: z.string().min(1, 'Campo obrigatório'),
    confirmPassword: z.string().min(1, 'Campo obrigatório'),
  })
  .refine(({ password, confirmPassword }) => password === confirmPassword, {
    message: 'As senhas não conferem',
    path: ['confirmPassword'],
  });

export type ChangeDefaultPasswordFormValues = z.infer<
  typeof changeDefaultPasswordFormSchema
>;
