import styled from 'styled-components';
import { Button, Heading } from '@onyma-ds/react';

export const Form = styled.form`
  max-width: 23rem;
  width: 100%;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacings.lg};
`;

export const FormHeader = styled.header``;

export const FormHeading = styled(Heading).attrs({
  type: 'heading_03',
})`
  font-weight: 600;
`;

export const FormBody = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacings.xxxs};
`;

export const Field = styled.div`
  display: grid;
  grid-template-rows: repeat(2, auto) 17px;
  gap: ${({ theme }) => theme.spacings.quark};
`;

export const Label = styled.label`
  color: ${({ theme }) => theme.colors.black};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  line-height: ${({ theme }) => theme.lineHeights.lg};
`;

export const ErrorMessage = styled.span`
  color: ${({ theme }) => theme.colors.danger};
  font-size: ${({ theme }) => theme.fontSizes.xs};
  line-height: ${({ theme }) => theme.lineHeights.lg};
`;

export const FormFooter = styled.footer``;

export const ChangePasswordButton = styled(Button).attrs({
  variant: 'secondary',
  color: 'white',
})`
  font-weight: 600;
  width: 100%;
  display: grid;
  place-items: center;
`;
