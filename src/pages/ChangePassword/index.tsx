import { useState } from 'react';
import { useParams } from 'react-router-dom';
import { HeroImageLayout } from '@/components';
import {
  ChangePasswordForm,
  ExpiredTokenError,
  PasswordChangedSuccessfully,
} from './components';

export default function ChangePasswordPage() {
  const { token } = useParams();
  const [status, setStatus] = useState<'idle' | 'success' | 'error'>('idle');

  const renderContent = () => {
    if (status === 'error') return <ExpiredTokenError />;
    if (status === 'success') return <PasswordChangedSuccessfully />;
    return (
      <ChangePasswordForm
        token={token as string}
        onSuccess={() => setStatus('success')}
        onError={() => setStatus('error')}
      />
    );
  };

  return <HeroImageLayout>{renderContent()}</HeroImageLayout>;
}
