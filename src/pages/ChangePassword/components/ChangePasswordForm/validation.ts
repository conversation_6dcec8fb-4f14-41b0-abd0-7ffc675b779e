import { z } from 'zod';

export const changePasswordFormSchema = z
  .object({
    newPassword: z.string().min(1, 'Campo obrigatório'),
    confirmPassword: z.string().min(1, 'Campo obrigatório'),
  })
  .refine(
    ({ newPassword, confirmPassword }) => newPassword === confirmPassword,
    {
      message: 'As senhas não conferem',
      path: ['confirmPassword'],
    },
  );

export type ChangePasswordFormValues = z.infer<typeof changePasswordFormSchema>;
