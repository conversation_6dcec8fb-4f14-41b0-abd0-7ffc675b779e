import { useState } from 'react';
import { HeroImageLayout } from '@/components';
import { LinkSentMessage, ResetPasswordForm } from './components';

export default function ResetPasswordPage() {
  const [email, setEmail] = useState<string | undefined>();

  const renderStep = () => {
    if (email) return <LinkSentMessage email={email} />;
    return <ResetPasswordForm onLinkSent={setEmail} />;
  };

  return <HeroImageLayout>{renderStep()}</HeroImageLayout>;
}
