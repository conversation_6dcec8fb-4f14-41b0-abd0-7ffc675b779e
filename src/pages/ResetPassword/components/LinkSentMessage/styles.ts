import styled from 'styled-components';
import { Button, Heading } from '@onyma-ds/react';

export const LinkSentContainer = styled.main`
  max-width: 23rem;
  width: 100%;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacings.lg};
`;

export const LinkSentHeading = styled(Heading).attrs({
  type: 'heading_03',
})`
  font-weight: 600;
  text-align: center;
`;

export const EmailHighlight = styled.span`
  color: ${({ theme }) => theme.colors.secondary};
`;

export const AccessMyAccountButton = styled(Button).attrs({
  variant: 'secondary',
  color: 'white',
})`
  font-weight: 600;
  width: 100%;
`;
