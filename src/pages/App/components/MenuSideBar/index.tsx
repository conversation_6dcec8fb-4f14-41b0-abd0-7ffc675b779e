import { Icons } from '@/components';
import { useMenuSideBar } from '@/contexts/menuSideBar';
import { useClickOutside, useWindowWidth } from '@/hooks';
import React, { useEffect } from 'react';
import { createPortal } from 'react-dom';
import { Links, LogoLink } from './components';
import * as SC from './styles';

export default function MenuSideBar() {
  const menuSideBarRef = React.useRef<HTMLDivElement>(null);

  const { screenWidth } = useWindowWidth();
  const { isOpen, onMenuOpenChange } = useMenuSideBar();

  useClickOutside(menuSideBarRef, () => {
    onMenuOpenChange(false);
  });

  const handleMenuOpenChange = () => {
    onMenuOpenChange((prevState) => !prevState);
  };

  useEffect(() => {
    if (screenWidth < 1200) onMenuOpenChange(false);
  }, [screenWidth, onMenuOpenChange]);

  return (
    <>
      {isOpen &&
        screenWidth < 1200 &&
        createPortal(<SC.Overlay id="overlay" />, document.body)}
      <SC.Container
        ref={menuSideBarRef}
        id="menu-side-bar"
        data-state={isOpen ? 'open' : 'closed'}
      >
        <LogoLink />
        <SC.ButtonCloseMenu
          type="button"
          onClick={handleMenuOpenChange}
        >
          <Icons.Symbol
            name="close"
            size={16}
            weight={700}
          />
        </SC.ButtonCloseMenu>

        <Links />
      </SC.Container>
    </>
  );
}
