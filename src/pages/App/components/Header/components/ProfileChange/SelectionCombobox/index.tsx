import { Combobox, InputBox, Text } from '@onyma-ds/react';
import { useState } from 'react';
import { ItemContainer } from './styles';
import { Option, Props } from './types';

export default function SelectionCombobox({
  data,
  value,
  onChange,
  label,
  disabled,
}: Props) {
  const [open, setOpen] = useState(false);

  const handleSelect = (value: Option) => {
    setOpen(false);
    onChange(value);
  };

  const optionSelectedFound = value
    ? data.find((option) => option.value === value)
    : null;

  return (
    <InputBox label={label}>
      <Combobox.Root
        open={open}
        onOpenChange={setOpen}
        modal
      >
        <Combobox.Trigger
          placeholder=""
          data-placeholder={!optionSelectedFound}
          disabled={disabled}
        >
          {optionSelectedFound
            ? optionSelectedFound.label
            : 'Selecionar empresa'}
        </Combobox.Trigger>
        <Combobox.Portal>
          <Combobox.Content placeholder="">
            <Combobox.Command>
              <Combobox.Input
                crossOrigin=""
                placeholder="Buscar..."
              />
              <Combobox.List>
                <Combobox.Empty>
                  <Text>Nenhum resultado encontrado</Text>
                </Combobox.Empty>
                <Combobox.Viewport>
                  <Combobox.Group>
                    {data.map((option: Option) => {
                      const companyLogo = option?.logo as string;
                      return (
                        <Combobox.Item
                          key={option.value}
                          placeholder=""
                          value={option.label}
                          onSelect={() => handleSelect(option)}
                        >
                          <ItemContainer>
                            <img
                              src={
                                option.logo
                                  ? companyLogo[0] === '/'
                                    ? `${import.meta.env.VITE_API_URL}/${option.logo}`
                                    : option.logo
                                  : 'https://placehold.co/24x24'
                              }
                            />
                            {option.label}
                          </ItemContainer>
                        </Combobox.Item>
                      );
                    })}
                  </Combobox.Group>
                </Combobox.Viewport>
              </Combobox.List>
            </Combobox.Command>
          </Combobox.Content>
        </Combobox.Portal>
      </Combobox.Root>
    </InputBox>
  );
}
