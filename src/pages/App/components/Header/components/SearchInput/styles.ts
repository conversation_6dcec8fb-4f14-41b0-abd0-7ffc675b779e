import styled from 'styled-components';

export const Container = styled.form`
  width: 100%;
  position: relative;
  display: flex;
  align-items: center;
`;

export const ControlInputView = styled.button`
  background-color: transparent;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
`;

export const InputBox = styled.section`
  max-width: 600px;
  flex-grow: 1;
  position: relative;

  &[data-platform='mobile'] {
    position: fixed;
    top: 3rem;
    left: 0;
    max-width: 100vw;
    width: 100vw;
  }
`;

export const IconBox = styled.button`
  background-color: ${({ theme }) => theme.colors.white};
  border: none;
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);

  &:hover {
    color: ${({ theme }) => theme.colors.secondary};
  }
`;
