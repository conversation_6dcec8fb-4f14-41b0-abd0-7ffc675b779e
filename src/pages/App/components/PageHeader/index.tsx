import { useNavigate } from 'react-router-dom';
import React, { PropsWithChildren } from 'react';
import { Heading } from '@onyma-ds/react';
import { Icons } from '@/components';
import * as SC from './styles';

function Root(props: PropsWithChildren) {
  return <SC.Container {...props} />;
}

function TitleBox(props: PropsWithChildren) {
  return <SC.TitleBox {...props}>{props.children}</SC.TitleBox>;
}

function Title({
  as = 'h3',
  type = 'heading_03',
  ...rest
}: React.ComponentPropsWithoutRef<typeof Heading>) {
  return (
    <Heading
      as={as}
      type={type}
      {...rest}
    />
  );
}

function BackButton() {
  const navigate = useNavigate();

  const handleBack = () => {
    navigate(-1);
  };

  return (
    <SC.BackButton
      type="button"
      onClick={handleBack}
    >
      <Icons.RAFa6.FaArrowLeft size={32} />
    </SC.BackButton>
  );
}

const PageHeader = {
  Root,
  TitleBox,
  Title,
  BackButton,
};

export default PageHeader;
