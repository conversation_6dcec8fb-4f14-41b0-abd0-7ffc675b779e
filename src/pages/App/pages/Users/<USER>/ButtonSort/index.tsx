import { Icons } from '@/components';
import * as SC from './styles';
import { Props } from './types';

export default function ButtonSort({
  children,
  type = 'button',
  sort,
  ...rest
}: Props) {
  return (
    <SC.Container
      {...rest}
      type={type}
    >
      {children}
      <SC.OrderBox>
        <Icons.RAFa.FaLongArrowAltUp
          size={14}
          data-state={sort === 'asc' ? 'active' : 'inactive'}
        />
        <Icons.RAFa.FaLongArrowAltDown
          size={14}
          data-state={sort === 'desc' ? 'active' : 'inactive'}
        />
      </SC.OrderBox>
    </SC.Container>
  );
}
