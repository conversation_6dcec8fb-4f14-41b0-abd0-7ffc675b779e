import { useState } from 'react';
import { Combobox, Text } from '@onyma-ds/react';
import { Props } from './types';

export default function OperatorCombobox({
  data,
  companySelected,
  onChange,
}: Props) {
  const [open, setOpen] = useState(false);

  const handleSelect = (value: string) => {
    setOpen(false);
    onChange(value);
  };

  const companySelectedFound = companySelected
    ? data.find((option) => option.id === companySelected)
    : null;

  return (
    <Combobox.Root
      open={open}
      onOpenChange={setOpen}
    >
      <Combobox.Trigger
        placeholder=""
        data-placeholder={!companySelectedFound}
      >
        {companySelectedFound
          ? companySelectedFound.name
          : 'Selecione a empresa'}
      </Combobox.Trigger>
      <Combobox.Portal>
        <Combobox.Content placeholder="">
          <Combobox.Command>
            <Combobox.List>
              <Combobox.Empty>
                <Text>Nenhum resultado encontrado</Text>
              </Combobox.Empty>
              <Combobox.Viewport>
                <Combobox.Group>
                  {data.map((option) => (
                    <Combobox.Item
                      key={option.id}
                      placeholder=""
                      value={option.name}
                      onSelect={() => handleSelect(option.id)}
                    >
                      {option.name}
                    </Combobox.Item>
                  ))}
                </Combobox.Group>
              </Combobox.Viewport>
            </Combobox.List>
          </Combobox.Command>
        </Combobox.Content>
      </Combobox.Portal>
    </Combobox.Root>
  );
}
