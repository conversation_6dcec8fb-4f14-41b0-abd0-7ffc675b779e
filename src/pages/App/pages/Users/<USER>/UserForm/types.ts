export type Props = {
  mode: 'edit' | 'create';
  user: {
    id: string;
    name: string;
    email: string;
    companies?: {
      id: string;
      nome: string;
    }[];
    isActive: boolean;
    profiles: string[];
    matriculaSoc?: string | null;
    codigoSoc?: string | null;
  } | null;
  onCancel: () => void;
  onCallback: () => Promise<void>;
};

export type Form = {
  name: string;
  email: string;
  companies: string[];
  password: string;
  profiles: string[];
  isActive: boolean;
  changePassword: boolean;
  codigoSoc?: string | null;
  matriculaSoc?: string | null;
};

export type Errors = {
  [key: string]: string;
};
