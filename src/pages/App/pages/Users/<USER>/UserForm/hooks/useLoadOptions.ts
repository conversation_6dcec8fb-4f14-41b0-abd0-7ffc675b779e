import { useCallback, useEffect, useState } from 'react';
import { useToast } from '@onyma-ds/react';
import { useApi } from '@/contexts/api';
import { Profile } from '@/services/api/users/remoteLoadProfiles';
import { ClientCompany } from '@/services/api/clients/remoteLoadClientCompanies';

export const useLoadOptions = () => {
  const { addToast } = useToast();
  const { users, clients } = useApi();

  const [profilesOptions, setProfilesOptions] = useState<Profile[]>([]);
  const [companiesOptions, setCompaniesOptions] = useState<ClientCompany[]>([]);

  const loadProfiles = useCallback(async () => {
    users
      .loadProfiles()
      .then((profilesResult) => {
        setProfilesOptions(profilesResult.result);
      })
      .catch((error) => {
        setProfilesOptions([]);
        const title = error.response?.data?.title;
        const description = error.response?.data?.message;
        addToast({
          type: 'error',
          title,
          description,
          timeout: 5000,
        });
      });
  }, [users, addToast]);

  const loadCompanies = useCallback(async () => {
    clients
      .loadClientCompanies()
      .then((loadClientCompaniesResult) => {
        const allActiveCompanies = loadClientCompaniesResult.result
          .filter((company) => company.isActive)
          .sort((a, b) => a.name.localeCompare(b.name));
        setCompaniesOptions(allActiveCompanies);
      })
      .catch((error) => {
        setCompaniesOptions([]);
        const title = error.response?.data?.title;
        const description = error.response?.data?.message;
        addToast({
          type: 'error',
          title,
          description,
          timeout: 5000,
        });
      });
  }, [clients, addToast]);

  useEffect(() => {
    Promise.all([loadProfiles(), loadCompanies()]);
  }, [loadProfiles, loadCompanies]);

  return { profilesOptions, companiesOptions };
};
