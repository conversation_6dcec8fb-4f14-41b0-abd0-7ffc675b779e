import { Spinner } from '@/components';
import { useApi } from '@/contexts/api';
import { purify } from '@/utils/purify';
import {
  Button,
  InputBox,
  InputTextBox,
  Switch,
  useToast,
} from '@onyma-ds/react';
import { useEffect, useState } from 'react';
import ComboboxCompany from './components/CompanyCheckCombobox';
import { useLoadOptions } from './hooks/useLoadOptions';
import * as SC from './styles';
import { Errors, Form, Props } from './types';
import { validateForm } from './validateForm';
import { LabelAndValue } from './components/CompanyCheckCombobox/types';

const initialForm: Form = {
  name: '',
  email: '',
  companies: [],
  password: '',
  isActive: true,
  changePassword: false,
  profiles: [],
  codigoSoc: '',
  matriculaSoc: '',
};

export default function UserForm({ mode, user, onCancel, onCallback }: Props) {
  const { auth, users } = useApi();
  const { addToast } = useToast();
  const { profilesOptions, companiesOptions } = useLoadOptions();
  const [selectedCompanies, setSelectedCompanies] = useState<LabelAndValue[]>(
    [],
  );

  const [loadingSubmit, setLoadingSubmit] = useState(false);
  const [errors, setErrors] = useState<Errors>({});
  const [form, setForm] = useState<Form>(initialForm);

  const handleComboboxChange = (field: string, value: string) => {
    setErrors((prevErrors) => ({ ...prevErrors, [field]: '' }));
    setForm((prevForm) => ({ ...prevForm, [field]: value }));
  };

  const handleFieldChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, checked } = event.target;
    setErrors((prevErrors) => ({ ...prevErrors, [name]: '' }));
    if (['changePassword', 'isActive'].includes(name)) {
      return setForm((prevForm) => ({ ...prevForm, [name]: checked }));
    }
    setForm((prevForm) => ({ ...prevForm, [name]: purify.sanitize(value) }));
  };

  const handleProfileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { id, checked } = event.target;
    setErrors((prevErrors) => ({ ...prevErrors, profiles: '' }));
    setForm((prevForm) => ({
      ...prevForm,
      profiles: checked
        ? [...prevForm.profiles, id]
        : prevForm.profiles.filter((profile) => profile !== id),
    }));
  };

  const create = async () => {
    const companiesIds = selectedCompanies.map((company) => company.value);
    try {
      setLoadingSubmit(true);
      const createResult = await users.createUser({
        nome: form.name,
        email: form.email,
        senha: form.password,
        empresas: companiesIds.filter((value) => value !== '') || null,
        ativo: true,
        perfis: form.profiles,
        codigoSoc: form.codigoSoc,
        matriculaSoc: form.matriculaSoc,
      });
      addToast({
        type: 'success',
        title: createResult.title,
        description: createResult.message,
        timeout: 5000,
      });
      await onCallback();
    } catch (error) {
      addToast({
        type: 'error',
        title: error.response?.data?.title,
        description: error.response?.data?.message || 'Erro ao criar usuário',
        timeout: 5000,
      });
    } finally {
      setLoadingSubmit(false);
    }
  };

  const edit = async () => {
    const companiesIds = selectedCompanies.map((company) => company.value);
    try {
      setLoadingSubmit(true);
      const editResult = await users.updateUser({
        id: user?.id as string,
        nome: form.name,
        email: form.email,
        senha: form.changePassword ? form.password : undefined,
        empresas: companiesIds.filter((value) => value !== '') || null,
        ativo: form.isActive,
        perfis: form.profiles,
        codigoSoc: form.codigoSoc,
        matriculaSoc: form.matriculaSoc,
      });
      if (form.changePassword) {
        const changePasswordResult = await auth.changePassword({
          userId: user?.id as string,
          password: form.password,
          passwordRepeat: form.password,
          checkCurrent: false,
        });
        addToast({
          type: 'success',
          title: changePasswordResult.title,
          description: changePasswordResult.message,
          timeout: 5000,
        });
      }
      addToast({
        type: 'success',
        title: editResult.title,
        description: editResult.message,
        timeout: 5000,
      });
      await onCallback();
    } catch (error) {
      addToast({
        type: 'error',
        title: error.response?.data?.title,
        description: error.response?.data?.message || 'Erro ao editar usuário',
        timeout: 5000,
      });
      setErrors((prevErrors) => ({
        ...prevErrors,
        profiles: error.response?.data?.message || 'Erro ao editar usuário',
      }));
    } finally {
      setLoadingSubmit(false);
    }
  };

  const handleSubmit = async (event: React.ChangeEvent<HTMLFormElement>) => {
    event.preventDefault();
    if (loadingSubmit) return;
    const validation = validateForm(form, mode);
    if (!validation.isValid) {
      return setErrors(validation.errors);
    }
    if (mode === 'create') {
      await create();
    }
    if (mode === 'edit') {
      await edit();
    }
  };

  useEffect(() => {
    const userCompanies = user?.companies?.map((company) => {
      return {
        label: company.nome,
        value: company.id,
      };
    });
    setSelectedCompanies(userCompanies || []);

    setForm((prevForm) => {
      return {
        ...prevForm,
        ...user,
        companies: user?.companies?.map((company) => company.id) || [],
      };
    });
  }, [companiesOptions, user]);

  return (
    <SC.Container onSubmit={handleSubmit}>
      <InputTextBox
        label="Nome"
        name="name"
        placeholder="Digite o nome"
        autoFocus
        value={form.name}
        onChange={handleFieldChange}
        error={!!errors.name}
        feedbackText={errors.name}
      />
      <InputTextBox
        label="E-mail"
        name="email"
        type="email"
        placeholder="Digite o E-mail"
        value={form.email}
        onChange={handleFieldChange}
        error={!!errors.email}
        feedbackText={errors.email}
      />
      <InputBox
        label="Empresa"
        error={!!errors.companyId}
        feedbackText={errors.companyId}
      >
        <ComboboxCompany
          onChange={(newCompanyId) =>
            handleComboboxChange('companyId', newCompanyId)
          }
          company={companiesOptions.map((company) => {
            return {
              label: company.name,
              value: company.id,
            };
          })}
          selectedCompanies={selectedCompanies}
          setSelectedCompanies={setSelectedCompanies}
        />
      </InputBox>
      <InputTextBox
        label="Matrícula SOC"
        name="matriculaSoc"
        placeholder="Digite a matrícula SOC"
        autoFocus
        value={form.matriculaSoc || ''}
        onChange={handleFieldChange}
        error={!!errors.matriculaSoc}
        feedbackText={errors.matriculaSoc}
      />
      <InputTextBox
        label="Código SOC"
        name="codigoSoc"
        placeholder="Digite o código SOC"
        autoFocus
        value={form.codigoSoc || ''}
        onChange={handleFieldChange}
        error={!!errors.codigoSoc}
        feedbackText={errors.codigoSoc}
      />
      {mode === 'edit' && (
        <SC.SwitchContainer>
          <Switch
            id="changePassword"
            name="changePassword"
            checked={form.changePassword}
            onChange={handleFieldChange}
          />
          <Switch.Label htmlFor="changePassword">Alterar senha?</Switch.Label>
        </SC.SwitchContainer>
      )}
      {!!(mode === 'create' || form.changePassword) && (
        <InputTextBox
          label="Senha"
          name="password"
          type="password"
          placeholder="Digite a senha"
          value={form.password}
          onChange={handleFieldChange}
          error={!!errors.password}
          feedbackText={errors.password}
        />
      )}
      <SC.SwitchContainer>
        <Switch
          id="isActive"
          name="isActive"
          checked={form.isActive}
          onChange={handleFieldChange}
        />
        <Switch.Label htmlFor="isActive">Ativo</Switch.Label>
      </SC.SwitchContainer>
      <SC.FieldsetAccess>
        <legend>Perfil de acesso</legend>
        <InputBox
          error={!!errors.profiles}
          feedbackText={errors.profiles}
        >
          <SC.FieldsAccess>
            {profilesOptions.map((profile) => (
              <SC.SwitchContainer key={profile.id}>
                <Switch
                  id={profile.id}
                  name="profile-item"
                  checked={form.profiles.includes(profile.id)}
                  onChange={handleProfileChange}
                />
                <Switch.Label htmlFor={profile.id}>{profile.nome}</Switch.Label>
              </SC.SwitchContainer>
            ))}
          </SC.FieldsAccess>
        </InputBox>
      </SC.FieldsetAccess>
      <SC.Buttons>
        <Button
          type="button"
          variant="secondary"
          buttonType="secondary"
          onClick={onCancel}
        >
          Cancelar
        </Button>
        <Button
          type="submit"
          variant="secondary"
          color="white"
        >
          {loadingSubmit ? (
            <Spinner
              size={16}
              color="white"
            />
          ) : mode === 'create' ? (
            'Cadastrar'
          ) : (
            'Editar'
          )}
        </Button>
      </SC.Buttons>
    </SC.Container>
  );
}
