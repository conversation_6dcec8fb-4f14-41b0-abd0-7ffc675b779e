import styled from 'styled-components';

export const Container = styled.section`
  height: 100%;
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: auto 1fr auto;
`;

export const FiltersContainer = styled.div`
  display: flex;
`;

export const ContainerLoading = styled.section`
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
`;

export const QuickFilterBox = styled.section`
  margin-left: auto;
  margin-bottom: 1.5rem;

  display: flex;
  gap: 1rem;

  > div {
    font-size: 0.875rem;
    font-weight: 500;
  }
`;

export const TableBox = styled.div`
  overflow: auto;
  position: relative;
`;

export const PaginationBox = styled.section`
  margin: 0 auto;

  & > div {
    flex-wrap: wrap;
  }
`;
