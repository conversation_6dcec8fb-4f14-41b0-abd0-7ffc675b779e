import { useEffect, useState } from 'react';
import { Dialog, useToast } from '@onyma-ds/react';
import { User } from '@/services/api/users/remoteLoadUser';
import { useApi } from '@/contexts/api';
import { useUsers } from '../../contexts/users';
import { UserForm } from '..';
import * as SC from './styles';
import { Props } from './types';

export default function ModalUser({ children, mode, userId }: Props) {
  const { addToast } = useToast();
  const { users: usersApi } = useApi();
  const { refetch } = useUsers();

  const [show, setShow] = useState(false);
  const [user, setUser] = useState<User | null>(null);

  const handleCallback = async () => {
    await refetch();
    setShow(false);
  };

  useEffect(() => {
    if (show && mode === 'edit' && userId) {
      usersApi
        .loadUser({ id: userId })
        .then((responseLoadUser) => {
          setUser(responseLoadUser.result);
        })
        .catch((error) => {
          addToast({
            type: 'error',
            title: error.response?.data?.title,
            description: error.response?.data?.message,
            timeout: 5000,
          });
        });
    }
  }, [show, mode, userId, usersApi, addToast]);

  return (
    <Dialog.Root
      open={show}
      onOpenChange={setShow}
    >
      <Dialog.Trigger asChild>{children}</Dialog.Trigger>
      {show && (
        <Dialog.Portal>
          <Dialog.Overlay />
          <SC.Content>
            <Dialog.CloseButton placeholder="">
              <Dialog.CloseIcon />
            </Dialog.CloseButton>
            <Dialog.Header>
              <Dialog.Title placeholder="">
                {mode === 'create' ? 'Novo' : 'Editar'} usuário
              </Dialog.Title>
            </Dialog.Header>
            <Dialog.Body>
              <UserForm
                mode={mode}
                user={{
                  id: user?.id || '',
                  name: user?.fullName || '',
                  email: user?.email || '',
                  companies: user?.companies,
                  isActive: user?.isActive || false,
                  codigoSoc: user?.codigoSoc || null,
                  matriculaSoc: user?.matriculaSoc || null,
                  profiles: user?.roles.map((i) => i.roleId) || [],
                }}
                onCancel={() => setShow(false)}
                onCallback={handleCallback}
              />
            </Dialog.Body>
          </SC.Content>
        </Dialog.Portal>
      )}
    </Dialog.Root>
  );
}
