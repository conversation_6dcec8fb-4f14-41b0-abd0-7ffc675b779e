import { User } from '@/services/api/users/remoteLoadUsers';
import { Sort } from '../types';

function compareDatesAsc(a: Date | null, b: Date | null): number {
  if (a === null && b === null) return 0;
  if (a === null) return -1;
  if (b === null) return 1;
  return a.getTime() - b.getTime();
}

// Função de comparação descendente
function compareDatesDesc(a: Date | null, b: Date | null): number {
  if (a === null && b === null) return 0;
  if (a === null) return 1;
  if (b === null) return -1;
  return b.getTime() - a.getTime();
}

export const applySort = (data: User[], sort: Sort) => {
  if (sort.field === 'nome') {
    return data.sort((a, b) => {
      if (sort.order === 'asc') {
        return a.nome < b.nome ? -1 : 1;
      }
      return a.nome > b.nome ? -1 : 1;
    });
  }

  if (sort.field === 'email') {
    return data.sort((a, b) => {
      if (sort.order === 'asc') {
        if (!a.email) return -1;
        if (!b.email) return 1;
        return a.email < b.email ? -1 : 1;
      }
      if (!a.email) return 1;
      if (!b.email) return -1;
      return a.email > b.email ? -1 : 1;
    });
  }

  if (sort.field === 'empresaNome') {
    return data.sort((a, b) => {
      if (sort.order === 'asc') {
        if (!a.empresaNome) return -1;
        if (!b.empresaNome) return 1;
        return a.empresaNome < b.empresaNome ? -1 : 1;
      }
      if (!a.empresaNome) return 1;
      if (!b.empresaNome) return -1;
      return a.empresaNome > b.empresaNome ? -1 : 1;
    });
  }

  if (sort.field === 'perfis') {
    return data.sort((a, b) => {
      const firstItemA = a.perfis[0]?.nomePerfil || '';
      const firstItemB = b.perfis[0]?.nomePerfil || '';
      if (sort.order === 'asc') {
        return firstItemA < firstItemB ? -1 : 1;
      }
      return firstItemA > firstItemB ? -1 : 1;
    });
  }

  if (sort.field === 'ativo') {
    return data.sort((a, b) => {
      if (sort.order === 'asc') {
        return a.ativo ? -1 : 1;
      }
      return b.ativo ? -1 : 1;
    });
  }

  if (sort.field === 'dataUltimoLogin') {
    return data.sort((a, b) => {
      if (sort.order === 'asc') {
        return compareDatesAsc(a.dataUltimoLoginDate, b.dataUltimoLoginDate);
      }
      return compareDatesDesc(a.dataUltimoLoginDate, b.dataUltimoLoginDate);
    });
  }

  if (sort.field === 'dataCadastro') {
    return data.sort((a, b) => {
      if (sort.order === 'asc') {
        return compareDatesAsc(a.dataCadastroDate, b.dataCadastroDate);
      }
      return compareDatesDesc(a.dataCadastroDate, b.dataCadastroDate);
    });
  }

  return data;
};
