import { replaceSpecialCharacters } from '@onyma-ds/react';
import { Filters } from '../types';
import { User } from '@/services/api/users/remoteLoadUsers';

export const applyFilters = (data: User[], filters: Filters) => {
  return data.filter((item) => {
    if (!filters.quickFilter) return true;
    const quickFilterClean = replaceSpecialCharacters(
      filters.quickFilter.trim().toLowerCase(),
    );
    const itemsQuickFilter = [
      replaceSpecialCharacters(item.nome.toLocaleLowerCase()),
      replaceSpecialCharacters(item.email.toLocaleLowerCase()),
      replaceSpecialCharacters(item.empresaNome?.toLocaleLowerCase() || ''),
    ].filter((item) => item);
    return itemsQuickFilter.some((itemFilter) =>
      itemFilter.includes(quickFilterClean),
    );
  });
};
