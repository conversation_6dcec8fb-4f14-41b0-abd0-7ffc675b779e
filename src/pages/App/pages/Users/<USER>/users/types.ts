import { Pagination } from '@/@types/Pagination';
import { User } from '@/services/api/users/remoteLoadUsers';

export type UsersContextData = {
  loading: { listing: boolean };
  data: User[];
  sort: Sort;
  filters: Filters;
  pagination: Pagination;
  refetch: () => Promise<void>;
  onSortChange: React.Dispatch<React.SetStateAction<Sort>>;
  onFiltersChange: React.Dispatch<React.SetStateAction<Filters>>;
  onPaginationChange: React.Dispatch<React.SetStateAction<Pagination>>;
};

export type Filters = {
  quickFilter?: string;
};

export type Sort = {
  field: string;
  order: 'asc' | 'desc';
};
