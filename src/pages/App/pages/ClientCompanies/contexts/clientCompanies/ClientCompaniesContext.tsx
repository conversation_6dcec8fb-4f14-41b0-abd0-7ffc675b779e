/* eslint-disable react-hooks/exhaustive-deps */
import {
  PropsWithChildren,
  createContext,
  useEffect,
  useMemo,
  useState,
} from 'react';
import { useToast } from '@onyma-ds/react';
import { useApi } from '@/contexts/api';
import {
  ClientCompaniesContextData,
  ClientCompany,
  Filters,
  Sort,
} from './types';
import { Pagination } from '@/@types/Pagination';
import { helpers } from './helpers';

export const ClientCompaniesContext =
  createContext<ClientCompaniesContextData | null>(null);

export const ClientCompaniesContextProvider = ({
  children,
}: PropsWithChildren) => {
  const { addToast } = useToast();
  const { clients } = useApi();

  const [loading, setLoading] = useState<boolean>(true);
  const [clientCompanies, setClientCompanies] = useState<ClientCompany[]>([]);
  const [sort, setSort] = useState<Sort>({ field: 'name', order: 'asc' });
  const [filters, setFilters] = useState<Filters>({});
  const [pagination, setPagination] = useState<Pagination>({
    page: 1,
    perPage: 10,
    totalItems: 0,
  });

  const loadClientCompanies = async () => {
    try {
      const resultClientCompanies = await clients.loadClientCompanies();
      setClientCompanies(resultClientCompanies.result);
      setPagination((prev) => ({
        ...prev,
        totalItems: resultClientCompanies.result.length,
      }));
    } catch (error) {
      setClientCompanies([]);
      addToast({
        type: 'error',
        title: error.response?.data?.title || 'Listagem das empresas clientes',
        description:
          error.response?.data?.message || 'Erro ao listar empresas clientes',
        timeout: 5000,
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadClientCompanies();
  }, []);

  const clientCompaniesMapped = useMemo(() => {
    return helpers.applyPagination(
      helpers.applyFilters(helpers.applySort(clientCompanies, sort), filters),
      pagination,
    );
  }, [clientCompanies, filters, pagination, sort]);

  return (
    <ClientCompaniesContext.Provider
      value={{
        loading,
        data: clientCompaniesMapped,
        allCompanies: clientCompanies,
        sort,
        filters,
        pagination,
        refetch: loadClientCompanies,
        onSortChange: setSort,
        onFiltersChange: setFilters,
        onPaginationChange: setPagination,
      }}
    >
      {children}
    </ClientCompaniesContext.Provider>
  );
};
