import { Pagination } from '@/@types/Pagination';
import { ClientCompany as ClientCompanyApi } from '@/services/api/clients/remoteLoadClientCompanies';

export type ClientCompaniesContextData = {
  loading: boolean;
  data: ClientCompany[];
  sort: Sort;
  filters: Filters;
  pagination: Pagination;
  allCompanies: ClientCompany[];
  refetch: () => Promise<void>;
  onSortChange: React.Dispatch<React.SetStateAction<Sort>>;
  onFiltersChange: React.Dispatch<React.SetStateAction<Filters>>;
  onPaginationChange: React.Dispatch<React.SetStateAction<Pagination>>;
};

export type ClientCompany = ClientCompanyApi;

export type Filters = {
  quickFilter?: string;
};

export type Sort = {
  field: string;
  order: 'asc' | 'desc';
};
