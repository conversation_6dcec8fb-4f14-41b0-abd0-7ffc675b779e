import { Switch } from '@onyma-ds/react';
import styled from 'styled-components';

export const Container = styled.form`
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
`;

export const SwitchContainer = styled(Switch.Container)`
  font-size: 1.25rem;

  label {
    font-size: 0.875rem;
  }
`;

export const OperatorBox = styled.div`
  a {
    width: min-content;
    white-space: nowrap;
    font-size: 0.875rem;
    background-color: ${({ theme }) => theme.colors.tertiary};
    color: ${({ theme }) => theme.colors.white};
    border-radius: 4px;
    padding: 4px 8px;
    transition: filter 0.2s;
    display: flex;
    align-items: center;
    gap: 4px;

    &:hover {
      text-decoration: underline;
      filter: brightness(0.9);
    }
  }
`;

export const Buttons = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: 0.5rem;

  button {
    display: grid;
    place-items: center;
  }

  @media (min-width: ${({ theme }) => `${theme.breakpoints.sm}px`}) {
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
  }
`;
