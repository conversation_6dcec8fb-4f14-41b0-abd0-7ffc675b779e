import React, { useEffect, useState } from 'react';
import {
  Button,
  InputBox,
  InputDateBox,
  InputTextBox,
  Switch,
  useToast,
} from '@onyma-ds/react';
import { Spinner } from '@/components';
import { useApi } from '@/contexts/api';
import { validateForm } from './validateForm';
import * as SC from './styles';
import { Errors, Form, Props } from './types';

const initialForm: Form = {
  name: '',
  createdAt: new Date(),
  isActive: true,
  includedBenSaude: false,
  codigoSoc: '',
  banner: '',
  logo: '',
};

export default function CompanyForm({
  mode,
  company,
  onCancel,
  onCallback,
}: Props) {
  const { addToast } = useToast();
  const { clients } = useApi();

  const [errors, setErrors] = useState<Errors>({});
  const [form, setForm] = useState<Form>(company || initialForm);
  const [loading, setLoading] = useState(false);

  const handleFieldChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, checked } = event.target;
    setErrors((prev) => ({ ...prev, [name]: '' }));
    if (['name', 'codigoSoc', 'logo'].includes(name)) {
      return setForm((prev) => ({ ...prev, [name]: value }));
    }
    setForm((prev) => ({ ...prev, [name]: checked }));
  };

  const create = async () => {
    try {
      setLoading(true);
      const createResult = await clients.createClientCompany({
        name: form.name,
        includedBenSaude: form.includedBenSaude,
        codigoSoc: form.codigoSoc,
        logo: form.logo,
      });
      addToast({
        type: 'success',
        title: createResult.title,
        description: createResult.message,
      });
      await onCallback();
    } catch (error) {
      const errorTitle =
        error.response?.data.title || 'Criação de empresa cliente';
      const errorMessage =
        error.response?.data.message || 'Falha ao criar empresa cliente';
      addToast({
        type: 'error',
        title: errorTitle,
        description: errorMessage,
        timeout: 5000,
      });
      setErrors((prev) => ({ ...prev, isActive: errorMessage }));
    } finally {
      setLoading(false);
    }
  };

  const edit = async () => {
    try {
      setLoading(true);
      const editResult = await clients.updateClientCompany({
        id: company?.id as string,
        name: form.name,
        includedBenSaude: form.includedBenSaude,
        isActive: form.isActive,
        codigoSoc: form.codigoSoc,
        logo: form.logo,
      });
      addToast({
        type: 'success',
        title: editResult.title,
        description: editResult.message,
      });
      await onCallback();
    } catch (error) {
      const errorTitle =
        error.response?.data.title || 'Edição de empresa cliente';
      const errorMessage =
        error.response?.data.message || 'Falha ao editar empresa cliente';
      addToast({
        type: 'error',
        title: errorTitle,
        description: errorMessage,
        timeout: 5000,
      });
      setErrors((prev) => ({ ...prev, isActive: errorMessage }));
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    const validation = validateForm(form);
    if (!validation.isValid) {
      return setErrors(validation.errors);
    }

    if (loading) return;

    if (mode === 'create') {
      await create();
    }

    if (mode === 'edit') {
      await edit();
    }
  };

  useEffect(() => {
    if (mode === 'create') {
      setForm((prev) => ({ ...prev, isActive: true }));
    }
  }, [mode]);

  return (
    <SC.Container onSubmit={handleSubmit}>
      <InputTextBox
        label="Nome*"
        name="name"
        placeholder="Digite o nome da empresa"
        value={form.name}
        onChange={handleFieldChange}
        error={!!errors.name}
        feedbackText={errors.name}
      />
      <InputTextBox
        label="Link da imagem"
        name="logo"
        placeholder="Digite o link da imagem da empresa"
        value={form.logo || ''}
        onChange={handleFieldChange}
        error={!!errors.logo}
        feedbackText={errors.logo}
      />
      <InputDateBox
        label="Data de cadastro*"
        selectedDate={form.createdAt}
        disabled
        error={!!errors.createdAt}
        feedbackText={errors.createdAt}
      />
      <InputTextBox
        label="Código SOC*"
        name="codigoSoc"
        placeholder="Digite o código SOC da empresa"
        value={form.codigoSoc || ''}
        onChange={handleFieldChange}
        error={!!errors.codigoSoc}
        feedbackText={errors.codigoSoc}
      />
      <InputBox
        error={!!errors.includedBenSaude}
        feedbackText={errors.includedBenSaude}
      >
        <SC.SwitchContainer>
          <Switch
            name="includedBenSaude"
            checked={form.includedBenSaude}
            onChange={handleFieldChange}
          />
          <Switch.Label>Empresa disponível no Ben+Saúde</Switch.Label>
        </SC.SwitchContainer>
      </InputBox>
      <InputBox
        error={!!errors.isActive}
        feedbackText={errors.isActive}
      >
        <SC.SwitchContainer>
          <Switch
            name="isActive"
            checked={form.isActive}
            onChange={handleFieldChange}
            disabled={mode === 'create'}
          />
          <Switch.Label>Ativo</Switch.Label>
        </SC.SwitchContainer>
      </InputBox>
      {/* {mode === 'edit' && (
        <SC.OperatorBox>
          <Link
            to="/app/empresas-clientes/clientCompanyId/operadoras"
            target="_blank"
          >
            Visualizar operadoras cadastradas
            <Icons.RAFa6.FaArrowUpRightFromSquare />
          </Link>
        </SC.OperatorBox>
      )} */}
      <SC.Buttons>
        <Button
          type="button"
          variant="secondary"
          buttonType="secondary"
          onClick={onCancel}
        >
          Cancelar
        </Button>
        <Button
          type="submit"
          variant="secondary"
          color="white"
        >
          {loading ? (
            <Spinner
              size={16}
              color="white"
            />
          ) : mode === 'create' ? (
            'Cadastrar'
          ) : (
            'Editar'
          )}
        </Button>
      </SC.Buttons>
    </SC.Container>
  );
}
