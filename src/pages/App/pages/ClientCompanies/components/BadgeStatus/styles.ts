import styled, { css } from 'styled-components';
import { Variant } from './types';

const variantTypes = {
  success: css`
    color: ${({ theme }) => theme.colors.white};
    background-color: ${({ theme }) => theme.colors.secondary};
  `,
  error: css`
    color: ${({ theme }) => theme.colors.white};
    background-color: ${({ theme }) => theme.colors.danger};
  `,
};

type ContainerProps = {
  $variant: Variant;
};

export const Container = styled.span<ContainerProps>`
  font-size: 0.875rem;
  padding: 2px 0.5rem;
  border-radius: 4px;

  ${({ $variant }) => variantTypes[$variant]}
`;
