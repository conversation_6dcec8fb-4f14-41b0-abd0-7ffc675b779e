import { useMemo } from 'react';
import { Table as TableDS } from '@onyma-ds/react';
import { Icons } from '@/components';
import { useClientCompanies } from '../../contexts/clientCompanies';
import { BadgeStatus, ButtonSort, ModalCompany } from '..';
import * as SC from './styles';

export default function Table() {
  const { data, sort, onSortChange } = useClientCompanies();

  const handleSort = (field: string) => {
    if (sort.field === field) {
      return onSortChange((prev) => ({
        ...prev,
        order: prev.order === 'asc' ? 'desc' : 'asc',
      }));
    }
    onSortChange((prev) => ({
      ...prev,
      field,
      order: 'asc',
    }));
  };

  const dataMapped = useMemo(() => {
    return data.map((item) => ({
      ...item,
      isActiveFormatted: item.isActive ? 'Sim' : 'Não',
      isActiveBadge: item.isActive
        ? 'success'
        : ('error' as 'success' | 'error'),
      includedBenSaudeFormatted: item.includedBenSaude ? 'Sim' : 'Não',
      includedBenSaudeBadge: item.includedBenSaude
        ? 'success'
        : ('error' as 'success' | 'error'),
    }));
  }, [data]);

  return (
    <SC.Container>
      <SC.Root>
        <TableDS.THead>
          <TableDS.Tr>
            <TableDS.Th
              colSpan={3}
              title="Nome"
            >
              <ButtonSort
                sort={sort.field === 'name' ? sort.order : null}
                onClick={() => handleSort('name')}
              >
                Nome
              </ButtonSort>
            </TableDS.Th>
            <TableDS.Th
              colSpan={1}
              title="Ativo"
            >
              <ButtonSort
                sort={sort.field === 'isActive' ? sort.order : null}
                onClick={() => handleSort('isActive')}
              >
                Ativo
              </ButtonSort>
            </TableDS.Th>
            <TableDS.Th
              colSpan={2}
              title="Incluso Ben + Saúde"
            >
              <ButtonSort
                sort={sort.field === 'includedBenSaude' ? sort.order : null}
                onClick={() => handleSort('includedBenSaude')}
              >
                Incluso Ben + Saúde
              </ButtonSort>
            </TableDS.Th>
            <TableDS.Th
              colSpan={2}
              title="Data de cadastro"
            >
              <ButtonSort
                sort={sort.field === 'createdAt' ? sort.order : null}
                onClick={() => handleSort('createdAt')}
              >
                Data de cadastro
              </ButtonSort>
            </TableDS.Th>
            <TableDS.Th>
              <ModalCompany mode="create">
                <SC.Trigger>
                  <Icons.Symbol
                    name="add"
                    size={16}
                  />{' '}
                  empresa
                </SC.Trigger>
              </ModalCompany>
            </TableDS.Th>
          </TableDS.Tr>
        </TableDS.THead>
        <TableDS.TBody>
          {dataMapped.map((item) => (
            <TableDS.Tr key={item.id}>
              <TableDS.Td colSpan={3}>{item.name}</TableDS.Td>
              <TableDS.Td colSpan={1}>
                <BadgeStatus variant={item.isActiveBadge}>
                  {item.isActiveFormatted}
                </BadgeStatus>
              </TableDS.Td>
              <TableDS.Td colSpan={2}>
                <BadgeStatus variant={item.includedBenSaudeBadge}>
                  {item.includedBenSaudeFormatted}
                </BadgeStatus>
              </TableDS.Td>
              <TableDS.Td colSpan={2}>{item.createdAtFormatted}</TableDS.Td>
              <TableDS.Td>
                <ModalCompany
                  mode="edit"
                  company={item}
                >
                  <SC.Trigger>
                    <Icons.Symbol
                      name="edit_square"
                      size={16}
                    />
                  </SC.Trigger>
                </ModalCompany>
              </TableDS.Td>
            </TableDS.Tr>
          ))}
        </TableDS.TBody>
      </SC.Root>
    </SC.Container>
  );
}
