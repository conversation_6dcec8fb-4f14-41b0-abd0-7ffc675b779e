import { useState } from 'react';
import { Dialog } from '@onyma-ds/react';
import { useClientCompanies } from '../../contexts/clientCompanies';
import { CompanyForm } from '..';
import * as SC from './styles';
import { Props } from './types';

export default function ModalCompany({ children, mode, company }: Props) {
  const { refetch } = useClientCompanies();

  const [show, setShow] = useState(false);

  const handleCallback = async () => {
    await refetch();
    setShow(false);
  };

  return (
    <Dialog.Root
      open={show}
      onOpenChange={setShow}
    >
      <Dialog.Trigger asChild>{children}</Dialog.Trigger>
      {show && (
        <Dialog.Portal>
          <Dialog.Overlay />
          <SC.Content>
            <Dialog.CloseButton placeholder="">
              <Dialog.CloseIcon />
            </Dialog.CloseButton>
            <Dialog.Header>
              <Dialog.Title placeholder="">
                {mode === 'create' ? 'Nova' : 'Editar'} empresa cliente
              </Dialog.Title>
            </Dialog.Header>
            <Dialog.Body>
              <CompanyForm
                mode={mode}
                company={company}
                onCallback={handleCallback}
                onCancel={() => setShow(false)}
              />
            </Dialog.Body>
          </SC.Content>
        </Dialog.Portal>
      )}
    </Dialog.Root>
  );
}
