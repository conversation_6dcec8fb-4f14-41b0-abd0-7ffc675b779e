import { Icons } from '@/components';
import { PageHeader } from '../../components';
import * as SC from './styles';
import DownloadManagementPage from './Page';
import { FiltersContextProvider } from './Contexts/filtersContextProvider';
import { useAuth } from '@/contexts/auth';

export default function DownloadManagement() {
  const { user } = useAuth();

  const isUserAdmin = user?.currentRole.name.includes('Administrador');
  return (
    <FiltersContextProvider>
      <SC.Container>
        <PageHeader.Root>
          <PageHeader.TitleBox>
            <Icons.RAFa6.FaFolderOpen size={24} />
            <PageHeader.Title>
              {isUserAdmin ? 'Gerenciamento' : 'Central'} de downloads
            </PageHeader.Title>
          </PageHeader.TitleBox>
          <PageHeader.BackButton />
        </PageHeader.Root>

        <DownloadManagementPage />
      </SC.Container>
    </FiltersContextProvider>
  );
}
