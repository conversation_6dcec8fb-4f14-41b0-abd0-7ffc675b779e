import { I<PERSON><PERSON>, Spinner } from '@/components';
import { Table as TableDS } from '@onyma-ds/react';

import { useAuth } from '@/contexts/auth';
import { IFile } from '@/services/api/file/remoteGetAllFiles/types';
import * as DropdownMenu from '@radix-ui/react-dropdown-menu';
import { HiOutlineDotsHorizontal } from 'react-icons/hi';
import { MdOutlineFileDownload } from 'react-icons/md';
import DeleteFileModal from '../DeleteFileModal';
import FileModal from '../FileForm';
import * as SC from './styles';
import { Link } from 'react-router-dom';

type TableProps = {
  isLoading?: boolean;
  files: IFile[];
  handleSelectAgenda: (file: IFile) => void;
  fileToEdit?: IFile;
};

export default function Table({
  isLoading,
  files,
  handleSelectAgenda,
  fileToEdit,
}: TableProps) {
  const { user } = useAuth();

  const isUserAdmin = user?.currentRole.name.includes('Administrador');

  if (isLoading) {
    return (
      <SC.ContainerLoading>
        <Spinner
          size={32}
          color="secondary"
        />
      </SC.ContainerLoading>
    );
  }

  return (
    <SC.Container>
      <SC.Root>
        <TableDS.THead>
          <TableDS.Tr>
            <TableDS.Th
              colSpan={1}
              title="Email"
            >
              Nome do arquivo
            </TableDS.Th>
            {isUserAdmin && (
              <TableDS.Th
                colSpan={1}
                title="Nome"
              >
                Empresa
              </TableDS.Th>
            )}
            {isUserAdmin && (
              <TableDS.Th
                colSpan={1}
                title="SOC"
              >
                Perfil
              </TableDS.Th>
            )}

            <TableDS.Th
              colSpan={1}
              title="phoneNumber"
            >
              Tipo do arquivo
            </TableDS.Th>
            <TableDS.Th
              colSpan={1}
              title="address"
            >
              Data
            </TableDS.Th>
            <TableDS.Th>
              {isUserAdmin && (
                <FileModal>
                  <SC.Trigger>
                    <Icons.Symbol
                      name="add"
                      size={16}
                    />
                    Novo arquivo
                  </SC.Trigger>
                </FileModal>
              )}
            </TableDS.Th>
          </TableDS.Tr>
        </TableDS.THead>
        <TableDS.TBody>
          {files.map((item) => (
            <TableDS.Tr key={item.id}>
              <TableDS.Td colSpan={1}>{item.nome}</TableDS.Td>
              {isUserAdmin && (
                <TableDS.Td colSpan={1}>{item.empresa.nome}</TableDS.Td>
              )}
              {isUserAdmin && (
                <TableDS.Td colSpan={1}>
                  {item.perfis.map((perfil) => perfil.nome)?.join(', ')}
                </TableDS.Td>
              )}
              <TableDS.Td colSpan={1}>{item.tipo.nome}</TableDS.Td>
              <TableDS.Td colSpan={1}>{item.dataCadastro}</TableDS.Td>
              <TableDS.Td
                style={{
                  display: 'flex',
                  justifyContent: 'center',
                  gap: '1rem',
                }}
              >
                {isUserAdmin ? (
                  <Link
                    to={item.url}
                    target="_blank"
                    download
                    style={{
                      width: '100%',
                      height: '100%',
                      maxHeight: '1.5rem',
                      maxWidth: '1.5rem',
                    }}
                  >
                    <SC.SquareButton>
                      <MdOutlineFileDownload />
                    </SC.SquareButton>
                  </Link>
                ) : (
                  <Link
                    to={item.url}
                    target="_blank"
                    download
                  >
                    <SC.Trigger>
                      <MdOutlineFileDownload size={16} />
                      Download
                    </SC.Trigger>
                  </Link>
                )}
                {isUserAdmin && (
                  <DropdownMenu.Root>
                    <DropdownMenu.Trigger
                      asChild
                      style={{ margin: '0' }}
                    >
                      <SC.Trigger type="button">
                        <HiOutlineDotsHorizontal size={14} />
                      </SC.Trigger>
                    </DropdownMenu.Trigger>
                    <DropdownMenu.Portal>
                      <DropdownMenu.Content
                        sideOffset={4}
                        align="end"
                      >
                        <SC.Content>
                          <FileModal
                            isEditMode
                            fileToEdit={fileToEdit}
                          >
                            <SC.GhostButton
                              color="black"
                              onClick={() => {
                                handleSelectAgenda(item);
                              }}
                            >
                              Substituir arquivo
                            </SC.GhostButton>
                          </FileModal>
                          <DeleteFileModal fileId={item.id}>
                            <SC.GhostButton color="danger">
                              Excluir arquivo
                            </SC.GhostButton>
                          </DeleteFileModal>
                        </SC.Content>
                      </DropdownMenu.Content>
                    </DropdownMenu.Portal>
                  </DropdownMenu.Root>
                )}
              </TableDS.Td>
            </TableDS.Tr>
          ))}
        </TableDS.TBody>
      </SC.Root>
    </SC.Container>
  );
}
