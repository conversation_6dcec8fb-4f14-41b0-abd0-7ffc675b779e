import { Checkbox, Combobox, Symbol } from '@onyma-ds/react';
import { theme } from '@onyma-ds/tokens';
import styled from 'styled-components';

export const Container = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
`;

export const Span = styled.span`
  font-size: ${theme.fontSizes.sm};
  font-weight: 600;
  color: ${theme.colors.secondary};
`;

export const FilterActions = styled.div`
  padding: 0.5rem 0;
  display: flex;
  justify-content: space-between;

  p {
    cursor: pointer;

    &:hover {
      text-decoration: underline;
    }
  }
`;

export const SelectedCompanies = styled.span`
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-transform: uppercase;
`;

export const Icon = styled(Symbol).attrs({
  size: 20,
})``;

export const ComboboxTrigger = styled(Combobox.Trigger)`
  &[data-placeholder='true'] {
    & ${Icon} {
      color: ${theme.colors.black};
    }
  }
`;

export const TriggerSpan = styled.span`
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;

  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
`;

export const ComboboxItem = styled(Combobox.Item)`
  cursor: pointer;
  &[data-current='true'] {
    background-color: ${theme.colors.white};
    border-color: ${theme.colors.white};
  }

  &:hover {
    background-color: ${theme.colors.tertiary};
    border-color: ${theme.colors.tertiary};

    & label {
      color: ${theme.colors.white};
    }
  }

  &:focus-visible {
    border-color: ${theme.colors.tertiary};
  }

  &[data-selected='true'] {
    border-color: ${theme.colors.tertiary};
  }
`;

export const GroupCheckboxContainer = styled(Checkbox.Container)`
  & label {
    font-size: ${theme.fontSizes.sm};
    font-weight: 600;
    color: ${theme.colors.secondary};
    text-transform: uppercase;
  }
`;

export const DateContainer = styled.div`
  display: flex;
  gap: 1rem;

  margin-top: 1rem;
`;
