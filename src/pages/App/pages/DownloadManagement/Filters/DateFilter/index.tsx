import { ErrorMessage } from '@/pages/Login/components/LoginForm/styles';
import { Combobox, InputDateBox } from '@onyma-ds/react';
import { Dispatch, SetStateAction, useEffect, useState } from 'react';
import { VscChevronDown } from 'react-icons/vsc';
import {
  Container,
  DateContainer,
  FilterActions,
  Span,
  TriggerSpan,
} from './styles';

type ComboboxProps = {
  label?: string;
  errorMessage?: string;
  startDateSelected?: Date;
  endDateSelected?: Date;
  setStartDateSelected: Dispatch<SetStateAction<Date | undefined>>;
  setEndDateSelected: Dispatch<SetStateAction<Date | undefined>>;
};

export function DateFilter({
  label,
  startDateSelected,
  endDateSelected,
  setEndDateSelected,
  setStartDateSelected,
}: ComboboxProps) {
  const [errorMessage, setErrorMessage] = useState<string | undefined>();
  const [open, setOpen] = useState(false);

  const isSomeSelected = startDateSelected || endDateSelected;

  useEffect(() => {
    if (
      startDateSelected &&
      endDateSelected &&
      startDateSelected > endDateSelected
    ) {
      setErrorMessage('Data inicial não pode ser maior que a data final');
    } else {
      setErrorMessage(undefined);
    }
  }, [startDateSelected, endDateSelected]);

  return (
    <Container>
      <Combobox.Root
        open={open}
        onOpenChange={setOpen}
      >
        <Combobox.Trigger
          placeholder=""
          style={{
            background: isSomeSelected ? '#333333' : '',
            color: isSomeSelected ? '#fff' : '',
          }}
        >
          <TriggerSpan>
            {label}
            <VscChevronDown />
          </TriggerSpan>
        </Combobox.Trigger>
        <Combobox.Portal>
          <Combobox.Content
            placeholder=""
            style={{ width: '350px' }}
          >
            <FilterActions>
              <Span>Filtro por {label}</Span>
              <p
                onClick={() => {
                  setStartDateSelected(undefined);
                  setEndDateSelected(undefined);
                }}
              >
                Limpar filtro
              </p>
            </FilterActions>
            <Combobox.Command>
              <Combobox.List>
                <Combobox.Viewport>
                  <Combobox.Group>
                    <DateContainer>
                      <InputDateBox
                        label="Data inicial"
                        selectedDate={startDateSelected}
                        onSelectDate={(date) => setStartDateSelected(date)}
                      />
                      <InputDateBox
                        label="Data final"
                        selectedDate={endDateSelected}
                        onSelectDate={(date) => setEndDateSelected(date)}
                      />
                    </DateContainer>
                    {!!errorMessage && (
                      <ErrorMessage>{errorMessage}</ErrorMessage>
                    )}
                  </Combobox.Group>
                </Combobox.Viewport>
              </Combobox.List>
            </Combobox.Command>
          </Combobox.Content>
        </Combobox.Portal>
      </Combobox.Root>
    </Container>
  );
}
