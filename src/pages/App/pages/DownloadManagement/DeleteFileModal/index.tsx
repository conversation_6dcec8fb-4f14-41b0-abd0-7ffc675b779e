import { Button, Dialog, useToast } from '@onyma-ds/react';

import * as SC from './styles';

import { useState } from 'react';
import { useApi } from '@/contexts/api';
import { useMutation, useQueryClient } from '@tanstack/react-query';

type DeleteFileModalProps = {
  children: React.ReactNode;
  fileId: string;
};

export default function DeleteFileModal({
  children,
  fileId,
}: DeleteFileModalProps) {
  const queryClient = useQueryClient();
  const { addToast } = useToast();
  const {
    file: { remoteDeleteFile },
  } = useApi();
  const [isOpen, setIsOpen] = useState(false);

  const { mutate } = useMutation({
    mutationFn: () => remoteDeleteFile({ id: fileId }),
    onSuccess: () => {
      addToast({
        title: 'Arquivo excluído com sucesso',
        description: 'O arquivo foi excluído com sucesso',
        type: 'success',
      });
      queryClient.invalidateQueries({ queryKey: ['fetch-all-files'] });
      setIsOpen(false);
    },
    onError: () => {
      addToast({
        title: 'Erro ao excluir arquivo',
        description: 'Ocorreu um erro ao excluir o arquivo',
        type: 'error',
      });
    },
  });

  const handleClose = () => {
    mutate();
  };

  return (
    <Dialog.Root
      open={isOpen}
      onOpenChange={() => setIsOpen((state) => !state)}
    >
      <Dialog.Trigger asChild>{children}</Dialog.Trigger>

      <Dialog.Portal>
        <Dialog.Overlay>
          <SC.Content>
            <Dialog.CloseButton placeholder="">
              <Dialog.CloseIcon />
            </Dialog.CloseButton>
            <Dialog.Header>
              <Dialog.Title placeholder="">Excluir Arquivo</Dialog.Title>
            </Dialog.Header>
            <Dialog.Body>
              <SC.Container>
                <span>
                  Tem certeza que deseja excluir o arquivo? Esta ação não poderá
                  ser desfeita.
                </span>
                <SC.WrapperButtons>
                  <Button
                    buttonType="secondary"
                    variant="secondary"
                    type="button"
                    onClick={handleClose}
                  >
                    Cancelar
                  </Button>
                  <Button
                    variant="danger"
                    type="submit"
                    color="white"
                    onClick={handleClose}
                  >
                    Sim, Excluir
                  </Button>
                </SC.WrapperButtons>
              </SC.Container>
            </Dialog.Body>
          </SC.Content>
        </Dialog.Overlay>
      </Dialog.Portal>
    </Dialog.Root>
  );
}
