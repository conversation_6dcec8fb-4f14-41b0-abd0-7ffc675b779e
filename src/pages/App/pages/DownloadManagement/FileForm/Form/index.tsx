import { useApi } from '@/contexts/api';
import { useAuth } from '@/contexts/auth';
import SelectCompany from '@/pages/App/components/Header/components/ProfileChange/CompanyCheckCombobox';
import { IFile } from '@/services/api/file/remoteGetAllFiles/types';
import { zodResolver } from '@hookform/resolvers/zod';
import { LuPaperclip } from 'react-icons/lu';

import {
  Button,
  InputBox,
  InputTextBox,
  SelectBox,
  useToast,
} from '@onyma-ds/react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import UserGroupMultiCombobox from '../../../SystemMenus/components/ModalCreate/Form/UserGroupMultiCombobox';
import { LabelAndValueCompromissos } from '../../../SystemMenus/components/ModalEdit/Form';
import { useLoadOptions } from '../../../Users/<USER>/UserForm/hooks/useLoadOptions';
import * as SC from './styles';
import { FileFormValues, fileFormSchema } from './validations';

type FileFormModalProps = {
  onClose: () => void;
  isEditMode?: boolean;
  fileToEdit?: IFile;
};

export function FileFormModal({
  onClose,
  isEditMode,
  fileToEdit,
}: FileFormModalProps) {
  const queryClient = useQueryClient();
  const { user } = useAuth();
  const { addToast } = useToast();
  const { companiesOptions, profilesOptions } = useLoadOptions();
  const {
    file: { loadFileTypes, remoteCreateFile, remoteEditFile },
  } = useApi();

  const [selectedUsersGroup, setSelectedUsersGroup] = useState<
    LabelAndValueCompromissos[]
  >([]);

  const {
    control,
    formState: { errors },
    setValue,
    handleSubmit,
    setError,
  } = useForm<FileFormValues>({
    resolver: zodResolver(fileFormSchema),
  });

  const { data: allFileTypes } = useQuery({
    queryKey: ['allFileTypes'],
    queryFn: () => loadFileTypes(),
    retry: 1,
    refetchOnWindowFocus: false,
    placeholderData: (previousData) => previousData,
  });

  const isUserAdmin = user?.currentRole.name.includes('Administrador');

  const { mutate: creationMutate } = useMutation({
    mutationFn: (params: FileFormValues) =>
      remoteCreateFile({
        arquivo: params.file as File,
        nome: params.fileName,
        empresa: params.company?.value,
        perfil: isUserAdmin ? params.userGroup : null,
        tipo: params.fileType.value,
        usuarioCriador: user?.uuid ?? '',
      }),
    onSuccess: () => {
      addToast({
        title: 'Sucesso',
        description: 'Arquivo criado com sucesso',
        type: 'success',
      });
      queryClient.invalidateQueries({ queryKey: ['fetch-all-files'] });
      onClose();
    },
    onError: () => {
      addToast({
        title: 'Erro',
        description: 'Erro ao criar arquivo',
        type: 'error',
      });
    },
  });

  const { mutate: editMutate } = useMutation({
    mutationFn: (params: FileFormValues) =>
      remoteEditFile({
        id: fileToEdit?.id as string,
        arquivo: params.file as File,
        nome: params.fileName,
        empresa: params.company.value,
        perfil: isUserAdmin ? params.userGroup : null,
        tipo: params.fileType.value,
        usuarioCriador: user?.uuid as string,
      }),
    onSuccess: () => {
      addToast({
        title: 'Sucesso',
        description: 'Arquivo atualizado com sucesso',
        type: 'success',
      });
      queryClient.invalidateQueries({ queryKey: ['fetch-all-files'] });
      onClose();
    },
    onError: () => {
      addToast({
        title: 'Erro ao atualizar arquivo',
        description:
          'Ocorreu um Erro ao editar arquivo. Tente novamente mais tarde',
        type: 'error',
      });
    },
  });

  const onCreate = (data: FileFormValues) => {
    creationMutate(data);
  };

  const onEdit = (data: FileFormValues) => {
    editMutate(data);
  };

  const onSubmit = (data: FileFormValues) => {
    if (!data.file && !fileToEdit?.url) {
      setError('file', { message: 'Esse campo é obrigatório' });
      return;
    }
    if (isEditMode) {
      onEdit(data);
      return;
    }
    onCreate(data);
  };

  useEffect(() => {
    if (fileToEdit) {
      setValue('fileName', fileToEdit.nome);
      setValue('fileType', {
        label: String(fileToEdit.tipo.nome),
        value: String(fileToEdit.tipo.id),
      });
      setValue(
        'company',
        {
          label: fileToEdit.empresa.nome,
          value: fileToEdit.empresa.id,
        },
        { shouldValidate: true },
      );
      setValue(
        'userGroup',
        fileToEdit.perfis.map((perfil) => perfil.nome).join(','),
      );

      const profiles: LabelAndValueCompromissos[] = fileToEdit.perfis.map(
        (perfil) => {
          return {
            label: perfil.nome,
            value: perfil.id,
            requerEmpresa: [], // used just to satisfy the type
          };
        },
      );
      setSelectedUsersGroup(profiles);
    }
  }, [fileToEdit, setValue]);

  return (
    <SC.Container onSubmit={handleSubmit(onSubmit)}>
      <Controller
        control={control}
        name="file"
        render={({ field }) => (
          <>
            <SC.FileUploadWrapper>
              <SC.TextInput
                type="text"
                placeholder={
                  isEditMode ? fileToEdit?.url : 'Selecione o arquivo'
                }
                value={field.value?.name ?? ''}
                disabled
              />
              <SC.StyledLabel htmlFor="file">
                <span>Selecionar</span>
                <LuPaperclip />
              </SC.StyledLabel>
              <SC.StyledInput
                id="file"
                type="file"
                onChange={(e) => {
                  const file = e.target.files?.[0];
                  field.onChange(file);
                }}
              />
            </SC.FileUploadWrapper>
            {errors.file && (
              <SC.ErrorMessage>{errors.file?.message}</SC.ErrorMessage>
            )}
          </>
        )}
      />

      <Controller
        control={control}
        name="fileName"
        render={({ field }) => (
          <InputTextBox
            id="fileName"
            type="text"
            placeholder="Insira o nome do arquivo"
            label="Nome do arquivo"
            value={field.value}
            error={!!errors.fileName}
            feedbackText={errors.fileName?.message}
            onChangeValue={field.onChange}
          />
        )}
      />

      <Controller
        control={control}
        name="fileType"
        render={({ field }) => (
          <SelectBox
            label="Tipo do arquivo"
            placeholder="Selecione o tipo do arquivo"
            options={
              allFileTypes?.result.map((company) => {
                return {
                  label: company.nome,
                  value: String(company.id),
                };
              }) ?? []
            }
            optionSelected={field.value}
            onSelect={(event) => {
              setValue('fileType', { label: '', value: '' });
              field.onChange(event);
            }}
            error={!!errors.fileType}
            feedbackText={errors.fileType?.message}
          />
        )}
      />
      {isUserAdmin && (
        <>
          <Controller
            control={control}
            name="company"
            render={({ field }) => (
              <InputBox label="Empresa">
                <SelectCompany
                  company={companiesOptions.map((company) => {
                    return {
                      label: company.name,
                      value: company.id,
                      logo: company.logo,
                    };
                  })}
                  selectedCompany={field.value}
                  setSelectedCompany={field.onChange}
                />
                {errors.company && (
                  <SC.ErrorMessage>{errors?.company.message}</SC.ErrorMessage>
                )}
              </InputBox>
            )}
          />

          <Controller
            control={control}
            name="userGroup"
            render={({ field }) => (
              <>
                <UserGroupMultiCombobox
                  label="Grupos de usuários"
                  company={
                    profilesOptions.map((profile) => {
                      return {
                        label: profile.nome,
                        value: profile.id,
                        requerEmpresa: profile.requerEmpresa,
                      };
                    }) ?? []
                  }
                  onChange={field.onChange}
                  selectedCompanies={selectedUsersGroup}
                  setSelectedCompanies={setSelectedUsersGroup}
                />
                {errors.userGroup && (
                  <SC.ErrorMessage>{errors?.userGroup.message}</SC.ErrorMessage>
                )}
              </>
            )}
          />
        </>
      )}

      <SC.WrapperButtons>
        <Button
          buttonType="secondary"
          variant="secondary"
          onClick={onClose}
          type="button"
        >
          Cancelar
        </Button>
        <Button
          variant="secondary"
          type="submit"
          color="white"
        >
          {isEditMode ? 'Editar' : 'Cadastrar'}
        </Button>
      </SC.WrapperButtons>
    </SC.Container>
  );
}
