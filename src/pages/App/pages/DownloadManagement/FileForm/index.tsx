import { Dialog } from '@onyma-ds/react';

import { useState } from 'react';
import { FileFormModal } from './Form';
import * as SC from './styles';
import { NewPageProps } from './types';

export default function FileModal({
  children,
  isEditMode,
  fileToEdit,
}: NewPageProps) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <Dialog.Root
      open={isOpen}
      onOpenChange={() => setIsOpen((state) => !state)}
    >
      <Dialog.Trigger asChild>{children}</Dialog.Trigger>

      <Dialog.Portal>
        <Dialog.Overlay>
          <SC.Content>
            <Dialog.CloseButton placeholder="">
              <Dialog.CloseIcon />
            </Dialog.CloseButton>
            <Dialog.Header>
              <Dialog.Title placeholder="">
                {isEditMode ? 'Editar' : 'Novo'} Arquivo
              </Dialog.Title>
            </Dialog.Header>
            <Dialog.Body>
              <FileFormModal
                onClose={() => setIsOpen((state) => !state)}
                isEditMode={isEditMode}
                fileToEdit={fileToEdit}
              />
            </Dialog.Body>
          </SC.Content>
        </Dialog.Overlay>
      </Dialog.Portal>
    </Dialog.Root>
  );
}
