import { useApi } from '@/contexts/api';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button, SelectBox, useToast } from '@onyma-ds/react';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { BiDownload, BiUpload } from 'react-icons/bi';
import { LuPaperclip } from 'react-icons/lu';

import { AxiosError } from 'axios';
import { Question } from '../../../FilesUpload/Question';
import { RadioContainer } from '../RadioContainer';
import * as SC from './styles';
import { uploadFileSchema, UploadFileSchemaType } from './validations';

export type IFileTypes = {
  id: number;
  modelo: string;
};

export default function FilesUploadPsicossocialPage() {
  const {
    import: { loadSendPsicossocialFile },
    forms: { loadForms },
  } = useApi();

  const { addToast } = useToast();
  const [selectedValue, setSelectedValue] = useState<IFileTypes | null>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const {
    handleSubmit,
    control,
    formState: { errors },
    reset,
  } = useForm<UploadFileSchemaType>({
    resolver: zodResolver(uploadFileSchema),
  });

  const { data: allFormsData } = useQuery({
    queryKey: ['forms'],
    queryFn: () => loadForms(),
    refetchOnWindowFocus: false,
    placeholderData: (previousData) => previousData,
    staleTime: 1000 * 60 * 30, // 30 minutes
  });

  const { mutate, isPending } = useMutation({
    mutationFn: (data: UploadFileSchemaType) =>
      loadSendPsicossocialFile({ file: data.file, formId: data.formId.value }),
    onSuccess: () => {
      addToast({
        title: 'Base SOC para Psicossocial importado com sucesso',
        description: 'O arquivo foi importado com sucesso.',
        type: 'success',
      });
      setSelectedValue(null);
      setErrorMessage(null);
      reset();
    },
    onError: (error: AxiosError<{ message: string }>) => {
      const errorData = error.response?.data;
      if (errorData) {
        setErrorMessage(errorData.message);
        addToast({
          title: 'Erro ao enviar arquivo',
          description: errorData.message,
          type: 'error',
        });
        return;
      }
      addToast({
        title: 'Erro ao enviar arquivo',
        description: error.message,
        type: 'error',
      });
    },
  });

  const handleRadioChange = (fileType: IFileTypes) => {
    setSelectedValue(fileType);
  };

  const onSubmit = (data: UploadFileSchemaType) => {
    mutate(data);
  };

  return (
    <SC.Container>
      <RadioContainer
        selectedValue={selectedValue}
        handleRadioChange={handleRadioChange}
      />
      <SC.FormWrapper onSubmit={handleSubmit(onSubmit)}>
        <Question
          index={1}
          label="Selecione o tipo do arquivo e faça o download do modelo do arquivo"
        >
          <a
            href={selectedValue?.modelo}
            download
            target="_blank"
            style={{ width: '100%' }}
          >
            <Button
              className="button"
              buttonType="secondary"
              variant="secondary"
              type="button"
              disabled={!selectedValue}
            >
              <BiDownload size={18} />
              Fazer download do modelo do arquivo
            </Button>
          </a>
        </Question>

        <Question
          index={2}
          label="Importe o arquivo após o preenchimento"
        >
          <Controller
            control={control}
            name="file"
            render={({ field }) => (
              <div style={{ width: '100%' }}>
                <SC.FileUploadWrapper>
                  <SC.TextInput
                    key={field.name}
                    type="text"
                    placeholder="Selecione o arquivo"
                    value={field.value?.name ?? ''}
                    disabled
                  />
                  <SC.StyledLabel
                    htmlFor="file"
                    disabled={selectedValue === null}
                  >
                    <span>Selecionar</span>
                    <LuPaperclip />
                  </SC.StyledLabel>
                  <SC.StyledInput
                    disabled={selectedValue === null}
                    id="file"
                    type="file"
                    accept=".csv"
                    onChange={(e) => {
                      const file = e.target.files?.[0];
                      field.onChange(file);
                    }}
                  />
                </SC.FileUploadWrapper>
                {errorMessage && (
                  <SC.ErrorMessage>{errorMessage}</SC.ErrorMessage>
                )}
                {errors.file && (
                  <SC.ErrorMessage>{errors.file?.message}</SC.ErrorMessage>
                )}
              </div>
            )}
          />
        </Question>
        <Question
          index={3}
          label="Selecione o formulário a ser utilizado"
        >
          <Controller
            control={control}
            name="formId"
            render={({ field: { onChange, value } }) => (
              <SelectBox
                placeholder="Selecione o formulário"
                options={
                  allFormsData?.result.map((form) => {
                    return {
                      label: String(form.name),
                      value: String(form.id),
                    };
                  }) ?? []
                }
                onSelect={(option) => onChange(option)}
                optionSelected={value}
                error={!!errors.formId}
                feedbackText={errors.formId?.message}
              />
            )}
          />
        </Question>

        <Button
          type="submit"
          className="button"
          color="white"
          variant="secondary"
          disabled={isPending || !selectedValue}
        >
          <BiUpload size={18} />
          Fazer upload do arquivo
        </Button>
      </SC.FormWrapper>
    </SC.Container>
  );
}
