import { ErrorMessage } from '@/pages/Login/components/LoginForm/styles';
import { Radio } from '@onyma-ds/react';
import { Container, RadioGroup, RadioWrapper } from './styles';
import { useQuery } from '@tanstack/react-query';
import { useApi } from '@/contexts/api';

import { Spinner } from '@/components';

import { useEffect } from 'react';
import { IFileTypes } from '../Form';

type RadioContainerProps = {
  selectedValue: IFileTypes | null;
  handleRadioChange: (id: IFileTypes) => void;
};

export function RadioContainer({
  selectedValue,
  handleRadioChange,
}: RadioContainerProps) {
  const {
    file: { remoteGetFileTypeById },
  } = useApi();

  const { data: fileType, isLoading } = useQuery({
    queryKey: ['allFileTypes'],
    queryFn: () => remoteGetFileTypeById(9),
    retry: 1,
    refetchOnWindowFocus: false,
    placeholderData: (previousData) => previousData,
  });

  useEffect(() => {
    if (fileType?.result) {
      handleRadioChange(fileType?.result as IFileTypes);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fileType]);

  if (isLoading) {
    return (
      <Spinner
        size={32}
        color="secondary"
      />
    );
  }

  return (
    <Container>
      <RadioGroup>
        <RadioWrapper
          key={fileType?.result.id}
          $isSelected={selectedValue?.id === fileType?.result.id}
          onClick={() => handleRadioChange(fileType?.result as IFileTypes)}
        >
          <Radio
            id={String(fileType?.result.id)}
            name={fileType?.result.nome}
            value={fileType?.result.id}
            checked={selectedValue?.id === fileType?.result.id}
          />
          <label htmlFor={String(fileType?.result.id)}>
            {fileType?.result.nome}
          </label>
        </RadioWrapper>
      </RadioGroup>

      {!selectedValue && (
        <ErrorMessage>* Esse campo é obrigatório.</ErrorMessage>
      )}
    </Container>
  );
}
