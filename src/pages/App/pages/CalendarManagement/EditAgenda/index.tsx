import { Dialog } from '@onyma-ds/react';

import { EditAgendaForm } from './Forms';
import * as SC from './styles';
import { NewGroupModalProps } from './types';

export default function EditAgendaModal({
  isOpen,
  setModalState,
  selectedAgenda,
}: NewGroupModalProps) {
  return (
    <Dialog.Root
      open={isOpen}
      onOpenChange={setModalState}
    >
      <Dialog.Portal>
        <Dialog.Overlay />
        <SC.Content>
          <Dialog.CloseButton placeholder="">
            <Dialog.CloseIcon />
          </Dialog.CloseButton>
          <Dialog.Header>
            <Dialog.Title placeholder="">Editar agenda</Dialog.Title>
            <Dialog.Subtitle placeholder="">
              Preencha os dados abaixo
            </Dialog.Subtitle>
          </Dialog.Header>
          <Dialog.Body>
            <EditAgendaForm
              onClose={setModalState}
              selectedAgenda={selectedAgenda}
            />
          </Dialog.Body>
        </SC.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
}
