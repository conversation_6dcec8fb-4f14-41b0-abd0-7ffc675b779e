import styled from 'styled-components';

export const Container = styled.form`
  display: flex;
  flex-direction: column;
  gap: 1.5rem;

  h2 {
    font-family: Roboto;
    font-size: 1rem;
    font-weight: 600;
    line-height: 1.4rem;
    color: ${({ theme }) => theme.colors.gray_30};
    margin-bottom: 0.75rem;
  }
`;

export const WrapperButtons = styled.div`
  width: 100%;
  display: flex;
  margin-top: 2rem;
  gap: 1rem;

  button {
    flex: 100%;
  }
`;
