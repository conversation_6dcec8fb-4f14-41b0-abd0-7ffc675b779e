/* eslint-disable react-hooks/exhaustive-deps */
import { InputTextBox, SelectBox } from '@onyma-ds/react';
import { Controller, useFormContext } from 'react-hook-form';

import { useApi } from '@/contexts/api';
import { telefoneMask } from '@/utils/formatters/telefoneMask';
import { useQuery } from '@tanstack/react-query';
import { CalendarType } from '../../../types';
import ComboboxCompromisso from '../../CompromissoMultiCombobox';
import { EditAgendaType } from '../validations';
import * as SC from './styles';
import { useEffect } from 'react';

interface FirstStepProps {
  defaultValues: CalendarType;
  selectedCompromissos: {
    label: string;
    value: string;
  }[];
  setSelectedCompromissos: React.Dispatch<
    React.SetStateAction<
      {
        label: string;
        value: string;
      }[]
    >
  >;
}

export function FirstStep({
  defaultValues,
  selectedCompromissos,
  setSelectedCompromissos,
}: FirstStepProps) {
  const {
    calendar: { remoteLoadCompromissos, remoteLoadExamsType },
  } = useApi();
  const {
    setValue,
    formState: { errors },
    control,
  } = useFormContext<EditAgendaType>();

  const { data: allCompromissos } = useQuery({
    queryKey: ['allCompromissos'],
    queryFn: () => remoteLoadCompromissos(),
  });

  const { data: allExamsType } = useQuery({
    queryKey: ['allExamsType'],
    queryFn: () => remoteLoadExamsType(),
  });

  useEffect(() => {
    if (allCompromissos) {
      const defaultCompromissos = defaultValues.tipoCompromisso
        .map((compromissoId) => {
          return allCompromissos.result.filter(
            (compromisso) => compromisso.codigoSoc === compromissoId,
          );
        })
        .flat();

      setSelectedCompromissos(
        defaultCompromissos.map((compromisso) => {
          return {
            label: compromisso.compromisso,
            value: compromisso.codigoSoc,
          };
        }),
      );
    }
  }, [allCompromissos, defaultValues.tipoCompromisso]);

  useEffect(() => {
    const selectedExam = allExamsType?.result.find(
      (examType) => examType.codigoSoc === defaultValues.tipoExame,
    );

    if (!selectedExam) return;

    setValue('exame', {
      label: selectedExam?.nome,
      value: selectedExam?.codigoSoc,
    });
  }, [allExamsType]);

  return (
    <SC.Container>
      <SC.Wrapper>
        <Controller
          control={control}
          name="nome"
          render={({ field }) => (
            <InputTextBox
              id="nome"
              type="text"
              placeholder="Digite o nome da agenda"
              label="Nome da agenda"
              defaultValue={defaultValues.nome}
              value={field.value}
              error={!!errors.nome}
              feedbackText={errors.nome?.message}
              onChangeValue={field.onChange}
            />
          )}
        />
        <Controller
          control={control}
          name="codigo"
          render={({ field }) => (
            <InputTextBox
              id="codigo"
              type="text"
              placeholder="Digite o código"
              label="Código SOC"
              defaultValue={defaultValues.codigo}
              value={field.value}
              error={!!errors.codigo}
              feedbackText={errors.codigo?.message}
              onChangeValue={field.onChange}
            />
          )}
        />
      </SC.Wrapper>
      <SC.Wrapper>
        <Controller
          control={control}
          name="exame"
          render={({ field: { onChange, value } }) => (
            <SelectBox
              label="Tipo do exame"
              placeholder="Selecione o tipo do exame"
              options={
                allExamsType?.result.map((compromisso) => {
                  return {
                    label: compromisso.nome,
                    value: compromisso.codigoSoc,
                  };
                }) ?? []
              }
              onSelect={(option) => onChange(option)}
              optionSelected={value}
              error={!!errors.exame}
              feedbackText={errors.exame?.message}
            />
          )}
        />
        <Controller
          control={control}
          name="compromisso"
          render={({ field }) => (
            <ComboboxCompromisso
              label="Tipo do compromisso"
              company={
                allCompromissos?.result.map((compromisso) => {
                  return {
                    label: compromisso.compromisso,
                    value: compromisso.codigoSoc,
                  };
                }) ?? []
              }
              onChange={field.onChange}
              selectedCompromissos={selectedCompromissos}
              setSelectedCompromissos={setSelectedCompromissos}
              errorMessage={errors.compromisso?.message}
            />
          )}
        />
      </SC.Wrapper>
      <SC.Wrapper>
        <Controller
          control={control}
          name="email"
          render={({ field }) => (
            <InputTextBox
              id="email"
              type="text"
              placeholder="Digite o email"
              label="Email"
              defaultValue={defaultValues.email}
              value={field.value}
              error={!!errors.email}
              feedbackText={errors.email?.message}
              onChangeValue={field.onChange}
            />
          )}
        />
        <Controller
          control={control}
          name="telefone"
          render={({ field }) => (
            <InputTextBox
              id="telefone"
              type="text"
              placeholder="Digite o telefone"
              label="Telefone"
              defaultValue={defaultValues.telefone}
              value={telefoneMask(field.value) ?? ''}
              error={!!errors.telefone}
              feedbackText={errors.telefone?.message}
              onChangeValue={field.onChange}
            />
          )}
        />
      </SC.Wrapper>
      <Controller
        control={control}
        name="endereco"
        render={({ field }) => (
          <InputTextBox
            id="endereco"
            type="text"
            placeholder="Digite o endereco"
            label="Endereço"
            defaultValue={defaultValues.endereco}
            value={field.value}
            error={!!errors.endereco}
            feedbackText={errors.endereco?.message}
            onChangeValue={field.onChange}
          />
        )}
      />
    </SC.Container>
  );
}
