import { format, addMinutes, startOfDay, endOfDay, isAfter } from 'date-fns';

export type SecondStepProps = {
  schedule: Schedule[];
  setSchedule: React.Dispatch<React.SetStateAction<Schedule[]>>;
  setScheduleErrors: React.Dispatch<React.SetStateAction<string[]>>;
  scheduleErrors: string[];
};

interface TimeSlot {
  startTime: string;
  endTime: string;
}
export interface Schedule {
  day: string;
  available: boolean;
  timeSlots: TimeSlot[];
}

export const scheduleArray = [
  {
    day: 'SEG',
    available: true,
    timeSlots: [{ startTime: '09:00', endTime: '18:00' }],
  },
  {
    day: 'TER',
    available: true,
    timeSlots: [{ startTime: '09:00', endTime: '18:00' }],
  },
  {
    day: 'QUA',
    available: true,
    timeSlots: [{ startTime: '09:00', endTime: '18:00' }],
  },
  {
    day: 'QUI',
    available: true,
    timeSlots: [{ startTime: '09:00', endTime: '18:00' }],
  },
  {
    day: 'SEX',
    available: true,
    timeSlots: [{ startTime: '09:00', endTime: '18:00' }],
  },
  { day: 'SÁB', available: false, timeSlots: [] },
  { day: 'DOM', available: false, timeSlots: [] },
];

export function generateTimeOptions(interval: number): string[] {
  const times: string[] = [];
  let currentTime = startOfDay(new Date());
  const endTime = endOfDay(new Date());

  // Gera horários até o final do dia
  while (isAfter(endTime, currentTime)) {
    times.push(format(currentTime, 'HH:mm'));
    currentTime = addMinutes(currentTime, interval);
  }
  return times;
}

export function dayName(dia: string): string {
  const mapeamentoDias: { [key: string]: string } = {
    segunda: 'Segunda',
    terca: 'Terça',
    quarta: 'Quarta',
    quinta: 'Quinta',
    sexta: 'Sexta',
    sabado: 'Sábado',
    domingo: 'Domingo',
  };

  return mapeamentoDias[dia] || dia;
}

export const convertToBackend = (
  schedule: Schedule[],
): { [key: string]: string[] | null } => {
  const daysMap: { [key: string]: string } = {
    SEG: 'segunda',
    TER: 'terca',
    QUA: 'quarta',
    QUI: 'quinta',
    SEX: 'sexta',
    SÁB: 'sabado',
    DOM: 'domingo',
  };

  const backendFormat: { [key: string]: string[] | null } = {
    segunda: null,
    terca: null,
    quarta: null,
    quinta: null,
    sexta: null,
    sabado: null,
    domingo: null,
  };

  schedule.forEach((item) => {
    const dayKey = daysMap[item.day];

    if (item.available) {
      backendFormat[dayKey] = item.timeSlots.map(
        (slot) => `${slot.startTime}-${slot.endTime}`,
      );
    } else {
      backendFormat[dayKey] = []; // Dias indisponíveis são representados como null
    }
  });

  return backendFormat;
};

export const converterDisponibilidade = (backendData: {
  [key: string]: string[] | null;
}): Schedule[] => {
  const daysMap: { [key: string]: string } = {
    segunda: 'SEG',
    terca: 'TER',
    quarta: 'QUA',
    quinta: 'QUI',
    sexta: 'SEX',
    sabado: 'SÁB',
    domingo: 'DOM',
  };

  return Object.keys(backendData).map((day) => {
    const dayKey = daysMap[day];

    if (backendData[day] === null) {
      // Se o dia é null, o dia não está disponível
      return {
        day: dayKey,
        available: false,
        timeSlots: [],
      };
    } else {
      // Se o dia tem horários, dividir o "startTime-endTime" em objetos
      const timeSlots = backendData[day]!.map((slot: string) => {
        const [startTime, endTime] = slot.split('-');
        return { startTime, endTime };
      });

      return {
        day: dayKey,
        available: true,
        timeSlots,
      };
    }
  });
};
