import styled from 'styled-components';

export const Container = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;

  border-radius: 0.5rem;
  background-color: ${({ theme }) => theme.colors.gray_98};
  padding: 1.5rem;

  gap: 1rem;
`;

export const CheckboxContainer = styled.div`
  display: flex;
`;

export const HourContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;

  gap: 1rem;
`;

export const WrapperContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  input[type='text'] {
    width: 82px;
  }
`;

export const Checkbox = styled.input`
  margin-right: 10px;
`;

export const Label = styled.span`
  width: 50px;
  font-weight: bold;
  margin-right: 10px;
`;

export const Trigger = styled.button`
  color: ${({ theme }) => theme.colors.white};
  background-color: ${({ theme }) => theme.colors.secondary_extra_light};
  border: 1px solid ${({ theme }) => theme.colors.secondary_extra_light};
  font-size: 0.75rem;
  padding: 0.25rem;
  border-radius: 0.25rem;

  &:hover {
    filter: brightness(0.9);
  }
`;

export const TriggerDelete = styled.button`
  color: ${({ theme }) => theme.colors.white};
  background-color: ${({ theme }) => theme.colors.danger};
  border: 1px solid ${({ theme }) => theme.colors.danger};

  padding: 0.25rem;
  border-radius: 0.25rem;

  &:hover {
    filter: brightness(0.9);
  }
`;
