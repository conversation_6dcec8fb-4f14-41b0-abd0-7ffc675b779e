import ScheduleItem from './ScheduleItem';
import * as SC from './styles';
import { SecondStepProps } from '../utils';

export function SecondStep({
  schedule,
  setSchedule,
  scheduleErrors,
  setScheduleErrors,
}: SecondStepProps) {
  const handleScheduleChange = (
    day: string,
    newTimeSlots: { startTime: string; endTime: string }[],
    available: boolean,
  ) => {
    setSchedule((prevSchedule) =>
      prevSchedule.map((scheduleItem) =>
        scheduleItem.day === day
          ? { ...scheduleItem, timeSlots: newTimeSlots, available }
          : scheduleItem,
      ),
    );
  };

  return (
    <SC.Container>
      {schedule.map((scheduleItem) => (
        <ScheduleItem
          key={scheduleItem.day}
          day={scheduleItem.day}
          available={scheduleItem.available}
          timeSlots={scheduleItem.timeSlots}
          onScheduleChange={handleScheduleChange}
          scheduleErrors={scheduleErrors}
          setScheduleErrors={setScheduleErrors}
        />
      ))}
    </SC.Container>
  );
}
