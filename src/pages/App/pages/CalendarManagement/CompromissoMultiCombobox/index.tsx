/* eslint-disable react-hooks/exhaustive-deps */
import { Checkbox, Combobox, Text } from '@onyma-ds/react';
import { Dispatch, SetStateAction, useEffect, useState } from 'react';
import { CompanyCheckboxContainer, Container, TriggerSpan } from './styles';
import { ErrorMessage, Label } from '@/pages/Login/components/LoginForm/styles';

type ComboboxCompanyProps = {
  label: string;
  errorMessage?: string;
  selectedCompromissos: {
    label: string;
    value: string;
  }[];
  company: {
    label: string;
    value: string;
  }[];
  onChange: (value: string) => void;
  setSelectedCompromissos: Dispatch<
    SetStateAction<
      {
        label: string;
        value: string;
      }[]
    >
  >;
};

export default function ComboboxCompromisso({
  company,
  onChange,
  selectedCompromissos,
  setSelectedCompromissos,
  label,
  errorMessage,
}: ComboboxCompanyProps) {
  const [open, setOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  const handleSelect = (selectedCompany: { label: string; value: string }) => {
    const isSelected = selectedCompromissos.some(
      (company) => company.value === selectedCompany.value,
    );

    if (isSelected) {
      setSelectedCompromissos((prev) =>
        prev.filter((company) => company.value !== selectedCompany.value),
      );
    } else {
      setSelectedCompromissos((prev) => [...prev, selectedCompany]);
    }
  };

  useEffect(() => {
    if (selectedCompromissos.length > 0) {
      onChange(selectedCompromissos.map((company) => company.value).join(','));
    } else {
      onChange('');
    }
  }, [selectedCompromissos]);

  const filteredCompanies = company.filter((comp) =>
    comp.label.toLowerCase().includes(searchTerm.toLowerCase()),
  );

  const selectedLabels = selectedCompromissos
    .map((company) => company.label)
    .join(', ');

  return (
    <Container>
      <Label>{label}</Label>
      <Combobox.Root
        open={open}
        onOpenChange={setOpen}
      >
        <Combobox.Trigger
          placeholder=""
          data-placeholder={!selectedLabels}
        >
          <TriggerSpan>
            {selectedLabels ? selectedLabels : 'Selecione...'}
          </TriggerSpan>
        </Combobox.Trigger>
        <Combobox.Portal>
          <Combobox.Content placeholder="">
            <Combobox.Command>
              <Combobox.Input
                crossOrigin=""
                placeholder="Buscar..."
                value={searchTerm}
                onValueChange={(e) => setSearchTerm(e)}
              />
              <Combobox.List>
                <Combobox.Empty>
                  <Text>Nenhum resultado encontrado</Text>
                </Combobox.Empty>
                <Combobox.Viewport>
                  <Combobox.Group>
                    {filteredCompanies.map((comp) => (
                      <CompanyCheckboxContainer key={comp.value}>
                        <Combobox.Item
                          placeholder=""
                          value={comp.value}
                          onSelect={() => handleSelect(comp)}
                        >
                          <Checkbox
                            id={comp.value}
                            name={comp.label}
                            value={comp.value}
                            checked={selectedCompromissos.some(
                              (selectedCompany) =>
                                selectedCompany.value === comp.value,
                            )}
                          />
                          <Checkbox.Label>{comp.label}</Checkbox.Label>
                        </Combobox.Item>
                      </CompanyCheckboxContainer>
                    ))}
                  </Combobox.Group>
                </Combobox.Viewport>
              </Combobox.List>
            </Combobox.Command>
          </Combobox.Content>
        </Combobox.Portal>
        {!!errorMessage && <ErrorMessage>{errorMessage}</ErrorMessage>}
      </Combobox.Root>
    </Container>
  );
}
