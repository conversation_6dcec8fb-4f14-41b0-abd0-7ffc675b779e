import { Button } from '@onyma-ds/react';

import * as SC from './styles';
import { DeleteAgendaState } from '../types';

export default function DeleteAgendaLayout({
  onClose,
  handleDeleteAgenda,
}: DeleteAgendaState) {
  return (
    <SC.Container>
      <span>
        Tem certeza que deseja excluir a agenda? Esta ação não poderá ser
        desfeita.
      </span>
      <SC.WrapperButtons>
        <Button
          buttonType="secondary"
          variant="secondary"
          onClick={onClose}
          type="button"
        >
          Cancelar
        </Button>
        <Button
          variant="danger"
          type="submit"
          color="white"
          onClick={handleDeleteAgenda}
        >
          Sim, Excluir
        </Button>
      </SC.WrapperButtons>
    </SC.Container>
  );
}
