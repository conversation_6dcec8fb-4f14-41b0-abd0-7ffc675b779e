import { Button } from '@onyma-ds/react';

import * as SC from './styles';

export default function DeleteAgendaErrorMessage({
  onClose,
}: {
  onClose: () => void;
}) {
  return (
    <SC.Container>
      <span>
        Não é possível excluir a agenda pois ela está sendo utilizada
        atualmente.
      </span>
      <SC.WrapperButtons>
        <Button
          variant="secondary"
          type="submit"
          color="white"
          onClick={onClose}
        >
          Ok, entendi!
        </Button>
      </SC.WrapperButtons>
    </SC.Container>
  );
}
