import { Dialog } from '@onyma-ds/react';
import { useState } from 'react';

import { remoteDeleteAppointment } from '@/services/api/calendar/remoteDeleteCalendar';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import DeleteAgendaErrorMessage from './ErrorMessage';
import DeleteAgendaLayout from './Form';
import * as SC from './styles';
import { DeleteAgendaProps } from './types';

export default function DeleteAgenda({
  isOpen,
  setModalState,
  agendaId,
}: DeleteAgendaProps) {
  const queryClient = useQueryClient();
  const [hasError, setHasError] = useState(false);

  const { mutate: deleteMutation } = useMutation({
    mutationFn: (id: number) => remoteDeleteAppointment({ id }),
    onSuccess: () => {
      setModalState();
      queryClient.invalidateQueries({ queryKey: ['allCalendars'] });
    },
    onError: () => {
      setHasError(true);
    },
  });

  const handleDeleteAgenda = () => {
    deleteMutation(agendaId);
  };

  return (
    <Dialog.Root
      open={isOpen}
      onOpenChange={setModalState}
    >
      <Dialog.Portal>
        <Dialog.Overlay />
        <SC.Content>
          <Dialog.CloseButton placeholder="">
            <Dialog.CloseIcon />
          </Dialog.CloseButton>
          <Dialog.Header>
            <Dialog.Title placeholder="">Excluir agenda</Dialog.Title>
          </Dialog.Header>
          <Dialog.Body>
            {hasError ? (
              <DeleteAgendaErrorMessage
                onClose={() => {
                  setHasError(false);
                  setModalState();
                }}
              />
            ) : (
              <DeleteAgendaLayout
                handleDeleteAgenda={handleDeleteAgenda}
                setHasError={setHasError}
                onClose={setModalState}
              />
            )}
          </Dialog.Body>
        </SC.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
}
