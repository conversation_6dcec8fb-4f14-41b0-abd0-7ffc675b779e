import { Icons } from '@/components';
import { Table as TableDS } from '@onyma-ds/react';

import * as SC from './styles';
import { CalendarType } from '../types';

type TableProps = {
  agendas: CalendarType[];
  onOpenAgendaCreation: () => void;
  onDeleteAgenda: (agenda: CalendarType) => void;
  handleSelectAgenda: (agenda: CalendarType) => void;
  onEditAgenda: () => void;
};

export default function Table({
  agendas,
  onOpenAgendaCreation,
  onDeleteAgenda,
  onEditAgenda,
  handleSelectAgenda,
}: TableProps) {
  return (
    <SC.Container>
      <SC.Root>
        <TableDS.THead>
          <TableDS.Tr>
            <TableDS.Th
              colSpan={1}
              title="Nome"
            >
              Nome da agenda
            </TableDS.Th>
            <TableDS.Th
              colSpan={1}
              title="SOC"
            >
              Código SOC
            </TableDS.Th>

            <TableDS.Th
              colSpan={1}
              title="Email"
            >
              Email
            </TableDS.Th>
            <TableDS.Th
              colSpan={1}
              title="phoneNumber"
            >
              Telefone
            </TableDS.Th>
            <TableDS.Th
              colSpan={1}
              title="address"
            >
              Endereço
            </TableDS.Th>
            <TableDS.Th>
              <SC.Trigger onClick={onOpenAgendaCreation}>
                <Icons.Symbol
                  name="add"
                  size={16}
                />
                Nova agenda
              </SC.Trigger>
            </TableDS.Th>
          </TableDS.Tr>
        </TableDS.THead>
        <TableDS.TBody>
          {agendas.map((item) => (
            <TableDS.Tr key={item.id}>
              <TableDS.Td colSpan={1}>{item.nome}</TableDS.Td>
              <TableDS.Td colSpan={1}>{item.codigo}</TableDS.Td>
              <TableDS.Td colSpan={1}>{item.email}</TableDS.Td>
              <TableDS.Td colSpan={1}>{item.telefone}</TableDS.Td>
              <TableDS.Td colSpan={1}>{item.endereco}</TableDS.Td>
              <TableDS.Td
                style={{
                  display: 'flex',
                  justifyContent: 'flex-end',
                  gap: '1rem',
                }}
              >
                <SC.SquareButton
                  onClick={() => {
                    handleSelectAgenda(item);
                    onEditAgenda();
                  }}
                >
                  <Icons.Symbol
                    name="edit_square"
                    size={16}
                  />
                </SC.SquareButton>
                <SC.SquareButton
                  type="button"
                  title="Excluir usuário"
                  variant="danger"
                  onClick={() => onDeleteAgenda(item)}
                >
                  <Icons.Symbol
                    name="delete"
                    size={16}
                  />
                </SC.SquareButton>
              </TableDS.Td>
            </TableDS.Tr>
          ))}
        </TableDS.TBody>
      </SC.Root>
    </SC.Container>
  );
}
