import { z } from 'zod';

const REQUIRED_MESSAGE = 'Esse campo é obrigatório.';

export const NewAgendaSchema = z.object({
  nome: z
    .string({ required_error: REQUIRED_MESSAGE })
    .min(3, 'O nome da agenda deve ter no mínimo 3 caracteres'),
  codigo: z.string({ required_error: REQUIRED_MESSAGE }),
  compromisso: z
    .string({ required_error: REQUIRED_MESSAGE })
    .min(1, 'Selecione um compromisso'),
  exame: z.object(
    {
      label: z.string(),
      value: z.string(),
    },
    { message: REQUIRED_MESSAGE },
  ),
  email: z
    .string({ message: REQUIRED_MESSAGE })
    .email({ message: 'Email inválido' }),
  telefone: z
    .string({
      required_error: REQUIRED_MESSAGE,
    })
    .refine(
      (arg) => {
        const removeChars = String(arg).replace(/[^\d]/g, '');
        return removeChars?.length >= 10;
      },
      {
        message: 'O celular deve ter 10 digitos',
      },
    ),
  endereco: z
    .string({ required_error: REQUIRED_MESSAGE })
    .min(3, 'Endereço inválido'),
});

export type NewAgendaType = z.infer<typeof NewAgendaSchema>;
