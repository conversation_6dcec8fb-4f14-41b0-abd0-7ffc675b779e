import { Icons } from '@/components';
import { PageHeader } from '../../components';
import { UserProfile } from './components';
import * as SC from './styles';

export default function UserProfileIdPage() {
  return (
    <SC.Container>
      <PageHeader.Root>
        <PageHeader.TitleBox>
          <Icons.RAFa6.FaFolderOpen size={24} />
          <PageHeader.Title>Editar perfil: Médico Cliente</PageHeader.Title>
        </PageHeader.TitleBox>
        <PageHeader.BackButton />
      </PageHeader.Root>
      <UserProfile />
    </SC.Container>
  );
}
