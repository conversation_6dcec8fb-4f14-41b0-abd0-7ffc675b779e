import { Switch } from '@onyma-ds/react';
import styled from 'styled-components';

export const Container = styled.form`
  display: flex;
  flex-direction: column;
  gap: 1.5rem;

  textarea {
    min-height: 120px;
  }
`;

export const SwitchContainer = styled(Switch.Container)`
  font-size: 1.25rem;

  label {
    font-size: 0.875rem;
  }
`;

export const Fieldset = styled.fieldset`
  border: none;
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;

  @media (min-width: ${({ theme }) => `${theme.breakpoints.sm}px`}) {
    grid-template-columns: 1fr 1fr;
  }
`;
