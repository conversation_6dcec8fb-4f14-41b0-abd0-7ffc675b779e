import { Menu } from './types';

export const data: Menu[] = [
  {
    id: (Math.random() * 1000).toString(),
    name: 'MENU',
    url: '',
    company: '',
    hasPermition: undefined,
    menus: [
      {
        id: (Math.random() * 1000).toString(),
        name: 'RISK REPORTS',
        url: 'https://app.powerbi.com/view?r=eyJrIjoiMDE0MzIyMzItMWQxZC00NDA4LThlY2YtMjQ4MTczMGFjZDU0IiwidCI6ImNkZjYwZjViLWI2YzEtNDk1OC04OTIzLTM3Y2ExMjgwNzM0YiJ9',
        company: 'Empresa Teste',
        hasPermition: false,
        menus: [],
      },
    ],
  },
  {
    id: (Math.random() * 1000).toString(),
    name: 'RELATÓRIOS',
    url: '',
    company: '',
    hasPermition: undefined,
    menus: [
      {
        id: (Math.random() * 1000).toString(),
        name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
        url: '#',
        company: 'Empresa Teste',
        hasPermition: false,
        menus: [
          {
            id: (Math.random() * 1000).toString(),
            name: '<PERSON><PERSON><PERSON>',
            url: 'https://portal.bencorp.com.br/EmpresaClienteResumoContrato',
            company: 'Empresa Teste',
            hasPermition: false,
            menus: [
              {
                id: (Math.random() * 1000).toString(),
                name: 'Random',
                url: 'https://portal.bencorp.com.br/EmpresaClienteResumoContrato',
                company: 'Empresa Teste',
                hasPermition: false,
                menus: [],
              },
            ],
          },
          {
            id: (Math.random() * 1000).toString(),
            name: 'Evolução da sinistralidade',
            url: 'https://portal.bencorp.com.br/menu#embedded',
            company: 'Empresa Teste',
            hasPermition: false,
            menus: [],
          },
        ],
      },
    ],
  },
  {
    id: (Math.random() * 1000).toString(),
    name: 'SISTEMA',
    url: '',
    company: '',
    hasPermition: undefined,
    menus: [
      {
        id: (Math.random() * 1000).toString(),
        name: 'Perfil de acesso',
        url: '#',
        company: 'Empresa Teste',
        hasPermition: false,
        menus: [
          {
            id: (Math.random() * 1000).toString(),
            name: 'Ver todos',
            url: 'https://portal.bencorp.com.br/perfil',
            company: 'Empresa Teste',
            hasPermition: false,
            menus: [],
          },
        ],
      },
      {
        id: (Math.random() * 1000).toString(),
        name: 'Menus do sistema (Só TI)',
        url: '#',
        company: 'Empresa Teste',
        hasPermition: false,
        menus: [
          {
            id: (Math.random() * 1000).toString(),
            name: 'Ver todos',
            url: 'https://portal.bencorp.com.br/menu',
            company: 'Empresa Teste',
            hasPermition: false,
            menus: [],
          },
          {
            id: (Math.random() * 1000).toString(),
            name: 'Novo menu',
            url: 'https://portal.bencorp.com.br/menu/novo',
            company: 'Empresa Teste',
            hasPermition: false,
            menus: [],
          },
        ],
      },
    ],
  },
];
