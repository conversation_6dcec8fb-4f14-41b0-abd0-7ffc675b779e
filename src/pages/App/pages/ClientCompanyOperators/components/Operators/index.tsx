import { InputText, Pagination } from '@onyma-ds/react';
import { Table } from '..';
import * as SC from './styles';

export default function Companies() {
  return (
    <SC.Container>
      <SC.QuickFilterBox>
        <InputText placeholder="Pesquisar..." />
      </SC.QuickFilterBox>
      <SC.TableBox>
        <Table />
      </SC.TableBox>
      <SC.PaginationBox>
        <Pagination
          currentPage={1}
          itemsPerPage={10}
          totalItems={100}
          itemsPerPageOptions={[10, 20, 30, 50, 100]}
          onPageChange={console.log}
          onItemsPerPageChange={console.log}
        />
      </SC.PaginationBox>
    </SC.Container>
  );
}
