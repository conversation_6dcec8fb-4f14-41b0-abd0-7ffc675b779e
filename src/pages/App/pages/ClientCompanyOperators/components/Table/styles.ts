import { Table } from '@onyma-ds/react';
import styled from 'styled-components';

export const Container = styled.div`
  flex: 1;
  min-width: 1400px;
  padding: 0 1rem;
  position: absolute;
  inset: 0;
`;

export const Root = styled(Table.Root)`
  height: min-content;

  th {
    text-align: left;
  }

  td {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }

  th,
  td {
    &:last-child {
      width: 6rem;
      text-align: right;

      & > div {
        display: flex;
        justify-content: flex-end;
        gap: 4px;
      }
    }
  }
`;

export const Trigger = styled.button`
  color: ${({ theme }) => theme.colors.white};
  background-color: ${({ theme }) => theme.colors.primary};
  border: 1px solid ${({ theme }) => theme.colors.primary};
  font-size: 0.75rem;
  padding: 4px;
  border-radius: 4px;
  transition: filter 0.2s;
  display: flex;
  align-items: center;
  gap: 4px;

  &:hover {
    filter: brightness(0.9);
  }
`;
