import { useState } from 'react';
import { Dialog } from '@onyma-ds/react';
import { OperatorForm } from '..';
import * as SC from './styles';
import { Props } from './types';

export default function ModalOperator({ children, mode }: Props) {
  const [show, setShow] = useState(false);

  return (
    <Dialog.Root
      open={show}
      onOpenChange={setShow}
    >
      <Dialog.Trigger asChild>{children}</Dialog.Trigger>
      {show && (
        <Dialog.Portal>
          <Dialog.Overlay />
          <SC.Content>
            <Dialog.CloseButton placeholder="">
              <Dialog.CloseIcon />
            </Dialog.CloseButton>
            <Dialog.Header>
              <Dialog.Title placeholder="">
                {mode === 'create' ? 'Nova' : 'Editar'} operadora
              </Dialog.Title>
            </Dialog.Header>
            <Dialog.Body>
              <OperatorForm
                mode={mode}
                onCancel={() => setShow(false)}
              />
            </Dialog.Body>
          </SC.Content>
        </Dialog.Portal>
      )}
    </Dialog.Root>
  );
}
