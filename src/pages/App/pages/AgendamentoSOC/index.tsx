import { Icons } from '@/components';
import { PageHeader } from '../../components';
import FormCreate from './components/Form/createAgendamento';
import * as SC from './styles';

export default function AgendamentoSOCFormPage() {
  return (
    <SC.Container>
      <PageHeader.Root>
        <PageHeader.TitleBox>
          <Icons.RAFa.FaEdit size={24} />
          <PageHeader.Title>Agendamento</PageHeader.Title>
        </PageHeader.TitleBox>
        <PageHeader.BackButton />
      </PageHeader.Root>
      <FormCreate />
    </SC.Container>
  );
}
