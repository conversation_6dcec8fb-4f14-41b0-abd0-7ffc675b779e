import { Spinner } from '@/components';
import { useApi } from '@/contexts/api';
import { useAuth } from '@/contexts/auth';
import {
  Button,
  Heading,
  InputDateBox,
  InputTextBox,
  SelectBox,
  useToast,
} from '@onyma-ds/react';
import React, { useState } from 'react';
import { useLoad } from './hooks/useLoad';
import * as SC from './styles';
import { Errors, Form as FormType } from './types';
import { validateForm } from './validateForm';

const examsTypesOptions: { label: string; value: string }[] = [
  {
    label: 'Admissional',
    value: 'ADMISSIONAL',
  },
  {
    label: 'Demissional',
    value: 'DEMISSIONAL',
  },
  {
    label: 'Periódico',
    value: 'PERIODICO',
  },
  {
    label: 'Mudança de função',
    value: 'MUDANCA_FUNCAO',
  },
  {
    label: 'Retorno ao trabalho',
    value: 'RETORNO_TRABALHO',
  },
  {
    label: 'Consulta assistencial',
    value: 'CONSULTA_ASSISTENCIAL',
  },
];

const initialForm: FormType = {
  companyId: '',
  employeeRegistration: '',
  employeeName: '',
  examType: '',
  calendar: '',
  date: undefined,
  hour: '',
};

export default function Form() {
  const { addToast } = useToast();
  const { user } = useAuth();
  const { calendar: calendarApi } = useApi();

  const [loadingSubmit, setLoadingSubmit] = useState<boolean>(false);
  const [errors, setErrors] = useState<Errors>({});
  const [form, setForm] = useState<FormType>(initialForm);

  const {
    messageToShow,
    companies,
    companiesOptions,
    calendarOptions,
    calendars,
    dateOptions,
    hoursOptions,
    availableDates,
  } = useLoad({
    calendarId: form.calendar,
    dateSelected: form.date,
  });

  const handleFormChange = (
    field: string,
    value: string | Date | undefined,
  ) => {
    setErrors((prevErrors) => ({ ...prevErrors, [field]: '' }));
    setForm((prevForm) => ({
      ...prevForm,
      [field]: value,
    }));
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    if (loadingSubmit) return;
    const validation = validateForm(form);
    if (!validation.isValid) {
      return setErrors(validation.errors);
    }
    setErrors({});

    setLoadingSubmit(true);
    try {
      const calendar = calendars.find(
        (item) => item.id.toString() === form.calendar,
      );
      const company = companies.find((comp) => comp.id === form.companyId);
      const dateAvailableSelected = availableDates.find(
        (date) =>
          date.data === form.date?.toLocaleDateString('pt-BR') &&
          date.horario === form.hour,
      );
      const resultSchedule = await calendarApi.socCreateSchedule({
        dataAgendamento: form.date?.toLocaleDateString('pt-BR') as string,
        horaInicio: form.hour as string,
        codigoUsuarioAgenda: calendar?.codigo as string,
        codigoFuncionario: user.socRegister,
        idTipoCompromisso: form.examType,
        detalhes: '',
        idEmpresaCliente: form.companyId,
        nomeAgenda: dateAvailableSelected?.nome as string,
        enderecoAgenda: calendar?.endereco as string,
        telefoneAgenda: calendar?.telefone as string,
        matricula: user.socRegister,
        nomeFuncionario: user.fullName,
        tipoEmail: 'AGENDAMENTO',
        idTipoExame: form.examType,
        textoLivre: '',
        emailPessoal: user.email,
        email: calendar?.email as string,
        codigoEmpresa: company?.codigoSoc as string,
      });
      addToast({
        type: 'success',
        title: resultSchedule.title,
        description: resultSchedule.message,
        timeout: 5000,
      });
      setForm(initialForm);
    } catch (error) {
      setErrors((prevErrors) => ({ ...prevErrors, email: error.message }));
    } finally {
      setLoadingSubmit(false);
    }
  };

  if (messageToShow) {
    return (
      <SC.ContainerFeedback>
        <Heading type="heading_04">{messageToShow}</Heading>
      </SC.ContainerFeedback>
    );
  }

  return (
    <SC.Container
      noValidate
      onSubmit={handleSubmit}
    >
      <SelectBox
        label="Selecione uma empresa"
        placeholder="Selecione uma empresa"
        options={companiesOptions.sort((a, b) =>
          a.label.localeCompare(b.label),
        )}
        optionSelected={companiesOptions.find(
          (o) => o.value === form.companyId,
        )}
        onSelect={(option) =>
          handleFormChange('companyId', option?.value || '')
        }
        error={!!errors.companyId}
        feedbackText={errors.companyId}
      />
      <InputTextBox
        label="Matrícula do colaborador"
        defaultValue={user.socRegister}
        readOnly
      />
      <InputTextBox
        label="Nome do colaborador"
        defaultValue={user.fullName}
        readOnly
      />
      <SelectBox
        label="Tipo de exame"
        placeholder="Selecione um tipo de exame"
        options={examsTypesOptions}
        optionSelected={examsTypesOptions.find(
          (o) => o.value === form.examType,
        )}
        onSelect={(option) => handleFormChange('examType', option?.value || '')}
        error={!!errors.examType}
        feedbackText={errors.examType}
      />
      <SelectBox
        label="Selecione a agenda"
        placeholder="Selecione a agenda"
        options={calendarOptions}
        optionSelected={calendarOptions.find((o) => o.value === form.calendar)}
        onSelect={(option) => handleFormChange('calendar', option?.value || '')}
        error={!!errors.calendar}
        feedbackText={errors.calendar}
      />
      <InputDateBox
        label="Datas disponíveis"
        availableDates={dateOptions}
        selectedDate={form.date}
        onSelectDate={(date) => handleFormChange('date', date)}
        disabled={!form.calendar}
        error={!!errors.date}
        feedbackText={errors.date}
      />
      <SelectBox
        label="Selecione um horário"
        placeholder="Selecione um horário"
        options={hoursOptions}
        optionSelected={hoursOptions.find((o) => o.value === form.hour)}
        onSelect={(option) => handleFormChange('hour', option?.value || '')}
        disabled={!form.date}
        error={!!errors.hour}
        feedbackText={errors.hour}
      />
      <InputTextBox
        label="E-mail pessoal"
        defaultValue={user.email}
        readOnly
      />
      <Button
        type="submit"
        variant="secondary"
        color="white"
      >
        {loadingSubmit ? (
          <Spinner
            size={16}
            color="white"
          />
        ) : (
          'Enviar'
        )}
      </Button>
    </SC.Container>
  );
}
