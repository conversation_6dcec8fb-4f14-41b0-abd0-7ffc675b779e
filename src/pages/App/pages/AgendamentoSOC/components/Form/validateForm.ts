import { Errors, Form } from './types';

export const validateForm = (form: Form) => {
  const errors: Errors = {};

  for (const [key, value] of Object.entries(form)) {
    switch (key) {
      case 'companyId': {
        if (!value) {
          errors.companyId = 'Campo obrigatório';
          break;
        }
        break;
      }
      case 'examType': {
        if (!value) {
          errors.examType = 'Campo obrigatório';
          break;
        }
        break;
      }
      case 'calendar': {
        if (!value) {
          errors.calendar = 'Campo obrigatório';
          break;
        }
        break;
      }
      case 'date': {
        if (!value) {
          errors.date = 'Campo obrigatório';
          break;
        }
        break;
      }
      case 'hour': {
        if (!value) {
          errors.hour = 'Campo obrigatório';
          break;
        }
        break;
      }
    }
  }

  return {
    isValid: Object.values(errors).every((error) => !error),
    errors,
  };
};
