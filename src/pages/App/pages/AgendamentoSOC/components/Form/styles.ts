import styled from 'styled-components';

export const Container = styled.form`
  max-width: 720px;
  width: 100%;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;

  & > button {
    display: grid;
    place-items: center;
  }
`;

export const ContainerFeedback = styled(Container)`
  color: ${({ theme }) => theme.colors.tertiary};
  text-align: center;
  margin-top: 4rem;
`;
