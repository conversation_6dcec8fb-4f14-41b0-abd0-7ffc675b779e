import styled from 'styled-components';
type DirectionType = 'top' | 'right' | 'bottom' | 'left';

interface ChevronProps {
  direction: DirectionType;
}

interface ContentWrapperProps {
  $maxHeight: number;
}

export const Chevron = styled.div<ChevronProps>`
  border-style: solid;
  border-width: 0.125rem 0.125rem 0 0;
  height: 0.25rem;
  width: 0.25rem;
  transition: all 0.25s ease-in-out;

  transform: ${(p) => p.direction === 'top' && 'rotate(-45deg)'};
  transform: ${(p) => p.direction === 'right' && 'rotate(45deg)'};
  transform: ${(p) => p.direction === 'bottom' && 'rotate(135deg)'};
  transform: ${(p) => p.direction === 'left' && 'rotate(-135deg)'};
`;

export const Container = styled.div`
  height: 100%;
  padding: 0 1.25rem;

  & + & {
    margin-top: -0.125rem;
  }

  hr {
    height: 1px;
    background-color: ${({ theme }) => theme.colors.gray_94};
    font-size: 0;
    border: 0;
  }
`;

export const Title = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 0;
  font-size: 1rem;
  font-weight: 700;
  line-height: 1.25;
  cursor: pointer;

  color: ${({ theme }) => theme.colors.secondary};
`;

export const ContentWrapper = styled.div<ContentWrapperProps>`
  max-height: ${(p) => `${p.$maxHeight}px`};
  transition: max-height 0.25s ease-in-out;
  overflow: hidden;
`;

export const Content = styled.div`
  padding: 0 0 1rem;
  color: rgba(0, 0, 0, 0.75);
  line-height: 1.5;
`;

export const VideoContainer = styled.div`
  width: 600px;
  height: 300px;
`;
