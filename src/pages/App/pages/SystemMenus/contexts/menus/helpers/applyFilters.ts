import { SystemMenu } from '..';
import { Filters, Menu } from '../types';

const reduceArrayName = (menus: Menu[], name: string) => {
  return menus.reduce((newItems, item) => {
    if (item.title.includes(name)) {
      newItems.push(item);
    } else {
      reduceArrayName(item.menus, name);
    }
    return newItems;
  }, [] as Menu[]);
};

const reduceArrayURL = (menus: Menu[], url: string) => {
  return menus.reduce((newItems, item) => {
    if (item.url.includes(url)) {
      newItems.push(item);
    } else {
      reduceArrayName(item.menus, url);
    }
    return newItems;
  }, [] as Menu[]);
};

export const applyFilters = (data: SystemMenu[], filters: Filters) => {
  let items = data;

  if (filters.name) {
    const filterName = filters.name;
    items = items.map((module) => {
      return {
        module: module.module,
        menus: reduceArrayName(module.menus, filterName),
      };
    });
  }

  if (filters.url) {
    const filterUrl = filters.url;
    items = items.map((module) => {
      return {
        module: module.module,
        menus: reduceArrayURL(module.menus, filterUrl),
      };
    });
  }

  return items;
};
