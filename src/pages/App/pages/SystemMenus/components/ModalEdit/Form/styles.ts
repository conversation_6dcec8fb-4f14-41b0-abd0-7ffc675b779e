import styled from 'styled-components';

export const Container = styled.form`
  display: flex;
  flex-direction: column;
  gap: 1.5rem;

  h2 {
    font-family: Roboto;
    font-size: 1rem;
    font-weight: 600;
    line-height: 1.4rem;
    color: ${({ theme }) => theme.colors.gray_30};
    margin-bottom: 0.75rem;
  }
`;

export const SwitchContainer = styled.div`
  font-size: 1.25rem;
  display: flex;
  label {
    font-size: 0.875rem;
  }
`;

export const CheckboxWrapper = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;

  label {
    font-family: Roboto;
    font-size: 0.875rem;
    font-weight: 400;
    line-height: 1.225rem;
  }
`;

export const WrapperButtons = styled.div`
  width: 100%;
  display: flex;
  gap: 1.5rem;

  button {
    flex: 50%;
  }
`;

export const ErrorMessage = styled.p`
  color: ${({ theme }) => theme.colors.danger};
  font-size: ${({ theme }) => theme.fontSizes.xs};
  line-height: ${({ theme }) => theme.lineHeights.lg};
`;

export const WrapperSection = styled.section`
  display: flex;
  flex-direction: column;
  gap: 1rem;
  > span {
    color: ${({ theme }) => theme.colors.primary};
    font-family: Roboto;
    font-size: 1rem;
    font-weight: 600;
    line-height: 1.4rem;
    text-align: left;
  }
`;

export const WrapperSectionModule = styled.section`
  display: flex;
  gap: 1rem;
  > span {
    color: ${({ theme }) => theme.colors.primary};
    font-family: Roboto;
    font-size: 1rem;
    font-weight: 600;
    line-height: 1.4rem;
    text-align: left;
  }
`;
