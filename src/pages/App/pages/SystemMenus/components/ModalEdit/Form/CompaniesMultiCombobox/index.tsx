/* eslint-disable react-hooks/exhaustive-deps */
import { Checkbox, Combobox, Text } from '@onyma-ds/react';
import { Dispatch, SetStateAction, useEffect, useState } from 'react';
import { CompanyCheckboxContainer, TriggerSpan } from './styles';
import { Label } from '@/pages/Login/components/LoginForm/styles';

type ComboboxCompanyProps = {
  label: string;
  selectedCompanies: {
    label: string;
    value: string;
  }[];
  company: {
    label: string;
    value: string;
  }[];
  onChange: (value: string) => void;
  setSelectedCompanies: Dispatch<
    SetStateAction<
      {
        label: string;
        value: string;
      }[]
    >
  >;
};

export default function ComboboxCompany({
  company,
  onChange,
  selectedCompanies,
  setSelectedCompanies,
  label,
}: ComboboxCompanyProps) {
  const [open, setOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  const handleSelect = (selectedCompany: { label: string; value: string }) => {
    const isSelected = selectedCompanies.some(
      (company) => company.value === selectedCompany.value,
    );
    if (isSelected) {
      setSelectedCompanies((prev) =>
        prev.filter((company) => company.value !== selectedCompany.value),
      );
    } else {
      setSelectedCompanies((prev) => [...prev, selectedCompany]);
    }
  };

  const handleSelectAll = () => {
    if (selectedCompanies.length === company.length) {
      setSelectedCompanies([]);
    } else {
      setSelectedCompanies(company);
    }
  };

  useEffect(() => {
    if (selectedCompanies.length > 0) {
      onChange(selectedCompanies.map((company) => company.value).join(','));
    } else {
      onChange('');
    }
  }, [selectedCompanies]);

  const filteredCompanies = company.filter((comp) =>
    comp.label.toLowerCase().includes(searchTerm.toLowerCase()),
  );

  const selectedLabels = selectedCompanies
    .map((company) => company.label)
    .join(', ');

  const allSelected = selectedCompanies.length === company.length;

  return (
    <div>
      <Label>{label}</Label>
      <Combobox.Root
        modal
        open={open}
        onOpenChange={setOpen}
      >
        <Combobox.Trigger
          placeholder=""
          data-placeholder={!selectedLabels}
        >
          <TriggerSpan>
            {selectedLabels ? selectedLabels : 'Selecione...'}
          </TriggerSpan>
        </Combobox.Trigger>
        <Combobox.Portal>
          <Combobox.Content placeholder="">
            <Combobox.Command>
              <Combobox.Input
                crossOrigin=""
                placeholder="Buscar..."
                value={searchTerm}
                onValueChange={(e) => setSearchTerm(e)}
              />
              <Combobox.List>
                <Combobox.Empty>
                  <Text>Nenhum resultado encontrado</Text>
                </Combobox.Empty>
                <Combobox.Viewport>
                  <Combobox.Group>
                    <CompanyCheckboxContainer>
                      <Combobox.Item
                        placeholder=""
                        value="selectAll"
                        onSelect={handleSelectAll}
                      >
                        <Checkbox
                          id="selectAll"
                          name="selectAll"
                          value="selectAll"
                          onChange={handleSelectAll}
                          checked={allSelected}
                        />
                        <Checkbox.Label>Selecionar todas</Checkbox.Label>
                      </Combobox.Item>
                    </CompanyCheckboxContainer>

                    {filteredCompanies.map((comp) => (
                      <CompanyCheckboxContainer key={comp.value}>
                        <Combobox.Item
                          placeholder=""
                          value={comp.label}
                          onSelect={() => handleSelect(comp)}
                        >
                          <Checkbox
                            id={comp.value}
                            name={comp.label}
                            value={comp.value}
                            checked={selectedCompanies.some(
                              (selectedCompany) =>
                                selectedCompany.value === comp.value,
                            )}
                          />
                          <Checkbox.Label>{comp.label}</Checkbox.Label>
                        </Combobox.Item>
                      </CompanyCheckboxContainer>
                    ))}
                  </Combobox.Group>
                </Combobox.Viewport>
              </Combobox.List>
            </Combobox.Command>
          </Combobox.Content>
        </Combobox.Portal>
      </Combobox.Root>
    </div>
  );
}
