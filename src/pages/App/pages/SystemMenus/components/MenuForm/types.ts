export type Props = {
  mode: 'edit' | 'create';
  menuId?: string;
  form?: Form;
  onCancel: () => void;
  onCallback: () => Promise<void>;
};

export type Form = {
  name: string;
  description: string;
  url: string;
  company: string;
  image: string;
  menuType: string;
  isSubmenu: boolean;
  submenu: string;
  menuTab: string;
  position: number;
  isActive: boolean;
  bi: string;
  biPage: string;
};

export type Errors = {
  [key: string]: string;
};

export type Option = {
  value: string;
  label: string;
};
