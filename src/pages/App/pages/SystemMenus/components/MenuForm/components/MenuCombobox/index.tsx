import { useState } from 'react';
import { Combobox, Text } from '@onyma-ds/react';
import * as SC from './styles';
import { Props } from './types';

export default function MenuCombobox({ data, menuSelected, onChange }: Props) {
  const [open, setOpen] = useState(false);

  const handleSelect = (value: string) => {
    setOpen(false);
    onChange(value);
  };

  const menuSelectedFound = menuSelected
    ? data.find((option) => option.id === menuSelected)
    : null;

  return (
    <Combobox.Root
      open={open}
      onOpenChange={setOpen}
    >
      <Combobox.Trigger
        placeholder=""
        data-placeholder={!menuSelectedFound}
      >
        {menuSelectedFound ? menuSelectedFound.title : 'Selecionar menu'}
      </Combobox.Trigger>
      <Combobox.Portal>
        <Combobox.Content placeholder="">
          <Combobox.Command>
            <Combobox.Input
              crossOrigin="true"
              placeholder="Buscar..."
            />
            <Combobox.List>
              <Combobox.Empty>
                <Text>Nenhum resultado encontrado</Text>
              </Combobox.Empty>
              <Combobox.Viewport>
                {data.map((option) => (
                  <Combobox.Item
                    key={option.id}
                    placeholder=""
                    value={option.title}
                    onSelect={() => handleSelect(option.id)}
                  >
                    <SC.ItemContent>
                      <Text type="body_03">{option.title}</Text>
                      <span>
                        <b>URL: </b>
                        {option.url}
                      </span>
                      <span>
                        <b>Empresa: </b>
                        Sem empresa
                      </span>
                    </SC.ItemContent>
                  </Combobox.Item>
                ))}
              </Combobox.Viewport>
            </Combobox.List>
          </Combobox.Command>
        </Combobox.Content>
      </Combobox.Portal>
    </Combobox.Root>
  );
}
