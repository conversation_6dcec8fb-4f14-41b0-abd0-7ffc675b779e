import { Icons } from '@/components';
import { Table as TableDS } from '@onyma-ds/react';
import { useCallback, useMemo } from 'react';
import { ModalFilters } from '..';
import { SystemMenu, useMenus } from '../../contexts/menus';
import NewPageModal from '../ModalCreate';
import { Row } from './row';
import * as SC from './styles';
import { MenuMapped } from './types';

export default function Table() {
  const { data } = useMenus();

  const reduceArray = useCallback(
    (menus: SystemMenu['menus'], iteration: number) => {
      return menus.reduce((newData, item) => {
        newData.push({
          ...item,
          menus: item.menus,
          type: item.type,
          menuIteration: iteration,
        });

        return newData;
      }, [] as MenuMapped[]);
    },
    [],
  );

  const dataToRender = useMemo(() => {
    return reduceArray(
      data.map((item) => ({
        ...item,
        id: item.module,
        title: item.module,
        url: '',
        position: '',
        type: null,
        description: '',
      })),
      0,
    );
  }, [reduceArray, data]);

  return (
    <SC.Container>
      <SC.Root>
        <TableDS.THead>
          <TableDS.Tr>
            <TableDS.Th
              colSpan={2}
              title="Nome"
            >
              Nome
            </TableDS.Th>
            <TableDS.Th
              colSpan={2}
              title="URL"
            >
              URL
            </TableDS.Th>
            <TableDS.Th
              colSpan={1}
              title="Posição"
            >
              Posição
            </TableDS.Th>
            <TableDS.Th
              colSpan={1}
              title="Tipo"
            >
              Tipo
            </TableDS.Th>
            <TableDS.Th>
              <SC.ThActions>
                <NewPageModal>
                  <SC.Trigger>
                    <Icons.Symbol
                      name="add"
                      size={16}
                    />
                    Página
                  </SC.Trigger>
                </NewPageModal>
                <ModalFilters />
              </SC.ThActions>
            </TableDS.Th>
          </TableDS.Tr>
        </TableDS.THead>
        <TableDS.TBody>
          {dataToRender.map((item) => (
            <Row
              key={item.id}
              data={{
                id: item.id,
                title: item.title,
                url: item.url,
                position: item.position,
                type: item.type,
                menus: item.menus,
              }}
            />
          ))}
        </TableDS.TBody>
      </SC.Root>
    </SC.Container>
  );
}
