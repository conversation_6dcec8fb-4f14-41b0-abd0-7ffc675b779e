import { Menu } from './types';

export const data: Menu[] = [
  {
    id: (Math.random() * 1000).toString(),
    title: 'MENU',
    url: '',
    position: '',
    type: 0,
    menus: [
      {
        id: (Math.random() * 1000).toString(),
        title: 'RISK REPORTS',
        url: 'https://app.powerbi.com/view?r=eyJrIjoiMDE0MzIyMzItMWQxZC00NDA4LThlY2YtMjQ4MTczMGFjZDU0IiwidCI6ImNkZjYwZjViLWI2YzEtNDk1OC04OTIzLTM3Y2ExMjgwNzM0YiJ9',
        position: '0',
        type: 0,
        menus: [],
      },
    ],
  },
  {
    id: (Math.random() * 1000).toString(),
    title: 'RELATÓRIOS',
    url: '',
    position: '',
    type: 0,
    menus: [
      {
        id: (Math.random() * 1000).toString(),
        title: 'Benefício<PERSON>',
        url: '#',
        position: '0',
        type: 0,
        menus: [
          {
            id: (Math.random() * 1000).toString(),
            title: 'Resumo',
            url: 'https://portal.bencorp.com.br/EmpresaClienteResumoContrato',
            position: '0',
            type: 0,
            menus: [
              {
                id: (Math.random() * 1000).toString(),
                title: 'Random',
                url: 'https://portal.bencorp.com.br/EmpresaClienteResumoContrato',
                position: '0',
                type: 0,
                menus: [],
              },
            ],
          },
          {
            id: (Math.random() * 1000).toString(),
            title: 'Evolução da sinistralidade',
            url: 'https://portal.bencorp.com.br/menu#embedded',
            position: '0',
            type: 0,
            menus: [],
          },
        ],
      },
    ],
  },
  {
    id: (Math.random() * 1000).toString(),
    title: 'SISTEMA',
    url: '',
    position: '',
    type: 0,
    menus: [
      {
        id: (Math.random() * 1000).toString(),
        title: 'Perfil de acesso',
        url: '#',
        position: '0',
        type: 0,
        menus: [
          {
            id: (Math.random() * 1000).toString(),
            title: 'Ver todos',
            url: 'https://portal.bencorp.com.br/perfil',
            position: '0',
            type: 0,
            menus: [],
          },
        ],
      },
      {
        id: (Math.random() * 1000).toString(),
        title: 'Menus do sistema (Só TI)',
        url: '#',
        position: '1',
        type: 0,
        menus: [
          {
            id: (Math.random() * 1000).toString(),
            title: 'Ver todos',
            url: 'https://portal.bencorp.com.br/menu',
            position: '1',
            type: 0,
            menus: [],
          },
          {
            id: (Math.random() * 1000).toString(),
            title: 'Novo menu',
            url: 'https://portal.bencorp.com.br/menu/novo',
            position: '0',
            type: 0,
            menus: [],
          },
        ],
      },
    ],
  },
];
