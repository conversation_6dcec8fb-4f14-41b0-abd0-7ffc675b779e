import { Table } from '@onyma-ds/react';
import { Link } from 'react-router-dom';
import styled, { css } from 'styled-components';

export const Container = styled.div`
  flex: 1;
  min-width: 900px;
  padding: 0 1rem;
  position: absolute;
  inset: 0;
`;

export const Root = styled(Table.Root)`
  height: min-content;

  td {
    text-overflow: ellipsis;
    overflow: hidden;
  }

  th,
  td {
    white-space: nowrap;

    &:last-child {
      width: 6rem;
      text-align: right;
    }
  }
`;

export const TdBox = styled.div`
  display: flex;
  align-items: center;
  gap: 4px;
`;

type MenuTabProps = {
  $span: number;
};

export const MenuTab = styled.span<MenuTabProps>`
  ${({ $span }) =>
    $span === 0
      ? css`
          display: none;
        `
      : css`
          width: ${$span}rem;
        `}
`;

export const LinkUrl = styled(Link)`
  &:hover {
    color: ${({ theme }) => theme.colors.primary};
  }
`;

export const Trigger = styled.button`
  color: ${({ theme }) => theme.colors.white};
  background-color: ${({ theme }) => theme.colors.primary};
  border: 1px solid ${({ theme }) => theme.colors.primary};
  font-size: 0.75rem;
  padding: 4px;
  margin-left: auto;
  border-radius: 4px;
  transition: filter 0.2s;
  display: flex;
  align-items: center;
  gap: 4px;

  &:hover {
    filter: brightness(0.9);
  }
`;

export const ThActions = styled.div`
  display: flex;
  align-items: center;
  gap: 4px;
`;
