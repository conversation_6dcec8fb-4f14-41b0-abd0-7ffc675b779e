import { I<PERSON><PERSON>, Spinner } from '@/components';
import { useApi } from '@/contexts/api';
import { Table as TableDS } from '@onyma-ds/react';
import { useMutation } from '@tanstack/react-query';
import { useState } from 'react';
import EditPageModal from '../ModalEdit';
import { ButtonExpand } from './components';
import * as SC from './styles';

interface RowData {
  id: string;
  title: string;
  url: string;
  position: string;
  type: number | null;
  menus?: RowData[];
}

interface RowProps {
  data: RowData;
  level?: number;
}

export function Row({ data, level = 0 }: RowProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [children, setChildren] = useState<RowData[] | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const {
    menus: { loadMenus, loadMenu },
  } = useApi();

  const isModule = data.type === null;

  const { mutateAsync } = useMutation({
    mutationFn: (menuId: string) => loadMenus({ modulo: menuId }),
  });

  const { mutateAsync: mutateMenu } = useMutation({
    mutationFn: (menuId: string) => loadMenu({ id: menuId }),
  });

  const handleExpand = async () => {
    if (!isExpanded) {
      setIsExpanded(true);
      if (!children) {
        setIsLoading(true);

        let fetchedData = null;
        let mappedData: RowData[] = [];

        if (isModule) {
          fetchedData = await mutateAsync(data.id);
          mappedData = fetchedData.result[0].menus.map((item) => ({
            id: item.id,
            title: item.title,
            url: item.url,
            position: item.position,
            type: item.type,
            menus: item.menus,
          }));
        } else {
          fetchedData = await mutateMenu(data.id);
          mappedData = fetchedData.result.menus[0].menus.map((item) => {
            return {
              id: item.id ?? item.uuid,
              title: item.title,
              url: item.url,
              position: item.position,
              type: item.type,
              menus: item.submenus ?? item.menus,
            };
          });
        }

        setChildren(mappedData);
        setIsLoading(false);
      }
    } else {
      setIsExpanded(false);
    }
  };

  return (
    <>
      <TableDS.Tr>
        <TableDS.Td colSpan={2}>
          <SC.TdBox>
            <SC.MenuTab $span={level} />
            {data.menus && (data.type === 5 || isModule) && (
              <ButtonExpand
                data-state={isExpanded ? 'expanded' : 'compressed'}
                onClick={handleExpand}
              />
            )}
            {data.title}
          </SC.TdBox>
        </TableDS.Td>
        <TableDS.Td colSpan={2}>
          <a
            href={data.url}
            target="_blank"
            rel="noopener noreferrer"
          >
            {data.url}
          </a>
        </TableDS.Td>
        <TableDS.Td colSpan={1}>{data.position}</TableDS.Td>
        <TableDS.Td colSpan={1}>{data.type}</TableDS.Td>
        {level > 0 && (
          <TableDS.Td>
            <EditPageModal menuId={data.id}>
              <SC.Trigger>
                <Icons.Symbol
                  name="edit_square"
                  size={16}
                />
              </SC.Trigger>
            </EditPageModal>
          </TableDS.Td>
        )}
      </TableDS.Tr>
      {isExpanded && (
        <>
          {isLoading ? (
            <TableDS.Tr>
              <Spinner />
            </TableDS.Tr>
          ) : (
            children &&
            children.map((child) => (
              <Row
                key={child.id}
                data={child}
                level={level + 1}
              />
            ))
          )}
        </>
      )}
    </>
  );
}
