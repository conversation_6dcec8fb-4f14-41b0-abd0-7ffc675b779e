import { Spinner } from '@/components';
import { Pagination } from '@onyma-ds/react';
import { useMenus } from '../../contexts/menus';
import Table from '../Table';
import * as SC from './styles';

export default function Menus() {
  const { loading, pagination, onPaginationChange } = useMenus();

  const handlePageChange = (page: number) => {
    onPaginationChange((prev) => ({ ...prev, page }));
  };

  const handleItemsPerPageChange = (perPage: number) => {
    onPaginationChange((prev) => ({ ...prev, perPage }));
  };

  return (
    <SC.Container>
      <SC.TableBox>
        {loading.listing && (
          <Spinner
            size={32}
            color="secondary"
          />
        )}
        {!loading.listing && <Table />}
      </SC.TableBox>
      <SC.PaginationBox>
        <Pagination
          currentPage={pagination.page}
          itemsPerPage={pagination.perPage}
          totalItems={pagination.totalItems}
          itemsPerPageOptions={[10, 20, 30, 50, 100]}
          onPageChange={handlePageChange}
          onItemsPerPageChange={handleItemsPerPageChange}
        />
      </SC.PaginationBox>
    </SC.Container>
  );
}
