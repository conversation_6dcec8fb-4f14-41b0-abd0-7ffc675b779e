/* eslint-disable react-hooks/exhaustive-deps */
import { useEffect, useState } from 'react';
import { Dialog, Heading, useToast } from '@onyma-ds/react';
import { useApi } from '@/contexts/api';
import { MenuComplete } from '@/services/api/menus/remoteLoadMenu';
import { Spinner } from '@/components';
import { useMenus } from '../../contexts/menus';
import { MenuForm } from '..';
import * as SC from './styles';
import { Props } from './types';

export default function ModalMenu(props: Props) {
  const { addToast } = useToast();
  const { menus } = useApi();
  const { refetch } = useMenus();

  const [loading, setLoading] = useState(props.mode === 'edit');
  const [show, setShow] = useState(false);
  const [menu, setMenu] = useState<MenuComplete>();

  const loadMenu = async (id: string) => {
    try {
      setLoading(true);
      const resultMenu = await menus.loadMenu({ id });
      setMenu(resultMenu.result);
    } catch (error) {
      addToast({
        type: 'error',
        title: error.response?.data.title || 'Carregando do Menu',
        description: error.response?.data.message || 'Erro ao carregar o menu',
        timeout: 5000,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCallback = async () => {
    await refetch();
    setShow(false);
  };

  useEffect(() => {
    if (show && props.mode === 'edit' && props.menuId) {
      loadMenu(props.menuId);
    }
  }, [show, props.mode, props.menuId]);

  const renderError = !loading && props.mode === 'edit' && !menu;

  return (
    <Dialog.Root
      open={show}
      onOpenChange={setShow}
    >
      <Dialog.Trigger asChild>{props.children}</Dialog.Trigger>
      {show && (
        <Dialog.Portal>
          <Dialog.Overlay />
          <SC.Content>
            <Dialog.CloseButton placeholder="">
              <Dialog.CloseIcon />
            </Dialog.CloseButton>
            <Dialog.Header>
              <Dialog.Title placeholder="">
                {props.mode === 'create' ? 'Novo' : 'Editar'} menu
              </Dialog.Title>
            </Dialog.Header>
            <Dialog.Body>
              {loading && (
                <Spinner
                  size={32}
                  color="secondary"
                />
              )}
              {renderError && (
                <SC.ErrorBox>
                  <Heading type="heading_06">
                    Erro ao carregar o menu. Não será possível editá-lo.
                  </Heading>
                </SC.ErrorBox>
              )}
              {!loading && !renderError && (
                <MenuForm
                  mode={props.mode}
                  menuId={menu?.id}
                  form={{
                    name: menu?.name || '',
                    url: menu?.url || '',
                    description: menu?.description || '',
                    company: '',
                    image: menu?.imagem || '',
                    menuType: menu?.menuTypeId?.toString() || '',
                    isSubmenu: menu?.submenu || false,
                    submenu: menu?.menuParentId || '',
                    menuTab: menu?.module || '',
                    position: menu?.order || 0,
                    isActive: menu?.ativo || false,
                    bi: menu?.biPagina?.biId?.toString() || '',
                    biPage: menu?.biPagina?.id.toString() || '',
                  }}
                  onCallback={handleCallback}
                  onCancel={() => setShow(false)}
                />
              )}
            </Dialog.Body>
          </SC.Content>
        </Dialog.Portal>
      )}
    </Dialog.Root>
  );
}
