import { Dialog } from '@onyma-ds/react';
import styled, { css } from 'styled-components';

export const Content = styled(Dialog.Content)`
  max-width: min(480px, 100vw);
  padding: 1.5rem;
  overflow-y: auto;
`;

export const DialogHeader = styled(Dialog.Header)`
  margin-bottom: 2rem;
`;

export const DialogTitle = styled(Dialog.Title)`
  text-align: left;
`;

type TriggerProps = {
  $hasAnyFilterApplied: boolean;
};

export const Trigger = styled.button<TriggerProps>`
  color: ${({ theme }) => theme.colors.black};
  background-color: ${({ theme }) => theme.colors.white};
  border: 1px solid ${({ theme }) => theme.colors.black};
  border-radius: 4px;
  padding: 4px;
  display: grid;
  place-content: center;
  transition: filter 0.2s;

  &:hover {
    filter: brightness(0.9);
  }

  ${({ theme, $hasAnyFilterApplied }) =>
    $hasAnyFilterApplied &&
    css`
      color: ${theme.colors.white};
      background-color: ${theme.colors.black};
    `}
`;

export const FieldsetFilters = styled.fieldset`
  border: none;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
`;
