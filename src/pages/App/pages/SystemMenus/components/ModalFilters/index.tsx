import { useEffect, useState } from 'react';
import { Button, Dialog, InputTextBox, SelectBox } from '@onyma-ds/react';
import { Icons } from '@/components';
import { useMenus } from '../../contexts/menus';
import { isActiveOptions, profileOptions, submenuOptions } from './options';
import * as SC from './styles';

const filterStateInitial = {
  name: '',
  url: '',
  isActive: '',
  profile: '',
  submenu: '',
};

export default function ModalFilters() {
  const { filters, onFiltersChange } = useMenus();

  const [show, setShow] = useState(false);
  const [filterState, setFilterState] =
    useState<typeof filters>(filterStateInitial);

  const handleFieldChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;
    setFilterState((prev) => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (field: string, value: string) => {
    setFilterState((prev) => ({ ...prev, [field]: value }));
  };

  const handleCleanFilters = () => {
    setFilterState(filterStateInitial);
  };

  const handleApplyFilters = () => {
    onFiltersChange({
      name: filterState.name,
      url: filterState.url,
      isActive: filterState.isActive,
      profile: filterState.profile,
      submenu: filterState.submenu,
    });
  };

  useEffect(() => {
    setFilterState({
      name: filters.name || '',
      url: filters.url || '',
      isActive: filters.isActive || '',
      profile: filters.profile || '',
      submenu: filters.submenu || '',
    });
  }, [filters]);

  const hasAnyFilterApplied = Object.values(filters).some((item) => !!item);

  return (
    <Dialog.Root
      open={show}
      onOpenChange={setShow}
    >
      <Dialog.Trigger asChild>
        <SC.Trigger $hasAnyFilterApplied={hasAnyFilterApplied}>
          <Icons.Symbol
            name="filter_list"
            size={16}
          />
        </SC.Trigger>
      </Dialog.Trigger>
      {show && (
        <Dialog.Portal>
          <Dialog.Overlay />
          <SC.Content>
            <Dialog.CloseButton placeholder="">
              <Dialog.CloseIcon />
            </Dialog.CloseButton>
            <SC.DialogHeader>
              <SC.DialogTitle placeholder="">Filtros</SC.DialogTitle>
            </SC.DialogHeader>
            <Dialog.Body>
              <SC.FieldsetFilters>
                <InputTextBox
                  label="Nome da página"
                  placeholder="Digite o nome da página"
                  name="name"
                  value={filterState.name}
                  onChange={handleFieldChange}
                />
                <InputTextBox
                  label="URL do menu"
                  placeholder="Digite a URL do menu"
                  name="url"
                  value={filterState.url}
                  onChange={handleFieldChange}
                />
                <SelectBox
                  label="Ativo"
                  placeholder="Selecione..."
                  options={isActiveOptions}
                  optionSelected={isActiveOptions.find(
                    (item) => item.value === filterState.isActive,
                  )}
                  onSelect={(option) =>
                    handleSelectChange('isActive', option.value)
                  }
                />
                <SelectBox
                  label="Perfil"
                  placeholder="Selecione o perfil"
                  options={profileOptions}
                  optionSelected={profileOptions.find(
                    (item) => item.value === filterState.profile,
                  )}
                  onSelect={(option) =>
                    handleSelectChange('profile', option.value)
                  }
                />
                <SelectBox
                  label="Submenu"
                  placeholder="Selecione o submenu"
                  options={submenuOptions}
                  optionSelected={submenuOptions.find(
                    (item) => item.value === filterState.submenu,
                  )}
                  onSelect={(option) =>
                    handleSelectChange('submenu', option.value)
                  }
                />
              </SC.FieldsetFilters>
            </Dialog.Body>
            <Dialog.Footer columns={2}>
              <Button
                type="button"
                variant="secondary"
                buttonType="secondary"
                onClick={handleCleanFilters}
              >
                Limpar filtros
              </Button>
              <Button
                type="button"
                variant="secondary"
                color="white"
                onClick={handleApplyFilters}
              >
                Filtrar
              </Button>
            </Dialog.Footer>
          </SC.Content>
        </Dialog.Portal>
      )}
    </Dialog.Root>
  );
}
