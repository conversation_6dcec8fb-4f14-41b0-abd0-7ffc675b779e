import styled from 'styled-components';

export const Container = styled.main`
  padding: 1rem 0.75rem;

  @media (min-width: ${({ theme }) => `${theme.breakpoints.md}px`}) {
    padding: 1.5rem;
  }
`;

export const Cards = styled.ul`
  width: 100%;
  padding: 1rem 0.75rem;
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
`;

export const CardItem = styled.li`
  list-style-type: none;
  display: flex;
  justify-content: center;
`;

export const EmptySubmenu = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
  padding: 1rem;
  font-size: 1rem;
  color: ${({ theme }) => theme.colors.gray_40};
  text-align: center;
`;
