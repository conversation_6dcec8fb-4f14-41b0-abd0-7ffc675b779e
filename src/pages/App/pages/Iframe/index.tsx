import { useSearchParams } from 'react-router-dom';
import * as SC from './styles';

export default function IframePage() {
  const [searchParams] = useSearchParams();
  const url = searchParams.get('url');

  if (!url) {
    return (
      <SC.Container>
        <h1>URL não informada</h1>
      </SC.Container>
    );
  }

  return (
    <SC.Container>
      <iframe src={url} />
    </SC.Container>
  );
}
