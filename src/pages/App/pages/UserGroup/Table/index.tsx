import { Icons } from '@/components';
import { Table as TableDS } from '@onyma-ds/react';
import { UserGroupType } from '../types';

import * as SC from './styles';
import { ResultItem } from '@/services/api/userGroup/remoteLoadUserGroup';

type TableProps = {
  userGroups: ResultItem[];
  setNewGroupModalState: () => void;
  setEditGroupModalState: () => void;
  handleSelectUserGroupToEdit: (userGroup: UserGroupType) => void;
  handleDeleteUserGroup: () => void;
};

export default function Table({
  userGroups,
  setNewGroupModalState,
  handleSelectUserGroupToEdit,
  setEditGroupModalState,
}: TableProps) {
  return (
    <SC.Container>
      <SC.Root>
        <TableDS.THead>
          <TableDS.Tr>
            <TableDS.Th
              colSpan={1}
              title="Nome"
            >
              Nome do Grupo
            </TableDS.Th>
            <TableDS.Th
              colSpan={1}
              title="Ativo"
            >
              Requer Empresa
            </TableDS.Th>

            <TableDS.Th>
              <SC.Trigger onClick={setNewGroupModalState}>
                <Icons.Symbol
                  name="add"
                  size={16}
                />{' '}
                Novo Grupo
              </SC.Trigger>
            </TableDS.Th>
          </TableDS.Tr>
        </TableDS.THead>
        <TableDS.TBody>
          {userGroups.map((item) => (
            <TableDS.Tr key={item.id}>
              <TableDS.Td colSpan={1}>{item.nome}</TableDS.Td>

              <TableDS.Td colSpan={1}>
                {item.requerEmpresa ? 'Sim' : 'Não'}
              </TableDS.Td>
              <TableDS.Td
                style={{
                  display: 'flex',
                  justifyContent: 'flex-end',
                  gap: '1rem',
                }}
              >
                <SC.SquareButton
                  onClick={() => {
                    setEditGroupModalState();
                    handleSelectUserGroupToEdit(item);
                  }}
                >
                  <Icons.Symbol
                    name="edit_square"
                    size={16}
                  />
                </SC.SquareButton>
                {/* <SC.SquareButton
                  type="button"
                  title="Excluir usuário"
                  variant="danger"
                  onClick={() => {
                    handleSelectUserGroupToEdit(item);
                    handleDeleteUserGroup();
                  }}
                >
                  <Icons.Symbol
                    name="delete"
                    size={16}
                  />
                </SC.SquareButton> */}
              </TableDS.Td>
            </TableDS.Tr>
          ))}
        </TableDS.TBody>
      </SC.Root>
    </SC.Container>
  );
}
