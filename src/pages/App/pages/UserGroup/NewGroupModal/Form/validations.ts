import { z } from 'zod';

export const NewUserGroupSchema = z.object({
  name: z
    .string({ required_error: 'Esse campo é obrigatório.' })
    .min(3, 'O nome do grupo deve ter no mínimo 3 caracteres'),
  description: z
    .string({ required_error: 'Esse campo é obrigatório.' })
    .min(3, 'A descrição do grupo deve ter no mínimo 3 caracteres'),
  requireCompany: z.boolean().optional(),
  // permission: z.object({
  //   start: z.boolean().optional(),
  //   app: z.boolean().optional(),
  //   indicators: z.boolean().optional(),
  //   faq: z.boolean().optional(),
  //   system: z.boolean().optional(),
  // }),
});

export type NewUserGroupType = z.infer<typeof NewUserGroupSchema>;
