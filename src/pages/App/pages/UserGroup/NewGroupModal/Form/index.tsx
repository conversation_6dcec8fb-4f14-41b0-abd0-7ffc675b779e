import { Button, Checkbox, InputTextBox, useToast } from '@onyma-ds/react';
import { Controller, SubmitHandler, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { NewUserGroupType, NewUserGroupSchema } from './validations';

import * as SC from './styles';
import { useApi } from '@/contexts/api';
import { useMutation, useQueryClient } from '@tanstack/react-query';

export default function NewUserGroupForm({ onClose }: { onClose: () => void }) {
  const queryClient = useQueryClient();
  const {
    userGroup: { createUserGroup },
  } = useApi();
  const toast = useToast();
  const {
    formState: { errors },
    handleSubmit,
    control,
  } = useForm<NewUserGroupType>({
    resolver: zodResolver(NewUserGroupSchema),
  });

  const { mutate } = useMutation({
    mutationFn: (data: NewUserGroupType) =>
      createUserGroup({
        name: data.name,
        requireCompany: !!data.requireCompany,
        description: data.description,
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['userGroups'] });
      toast.addToast({
        type: 'success',
        title: 'Grupo criado com sucesso',
        description: 'O grupo foi criado com sucesso',
      });
      onClose();
    },
    onError: () => {
      toast.addToast({
        type: 'error',
        title: 'Erro ao criar grupo',
        description: 'Ocorreu um erro ao criar o grupo',
      });
    },
  });

  const handleSubmitForm: SubmitHandler<NewUserGroupType> = (data) => {
    mutate(data);
  };

  return (
    <SC.Container onSubmit={handleSubmit(handleSubmitForm)}>
      <Controller
        control={control}
        name="name"
        render={({ field }) => (
          <InputTextBox
            id="name"
            type="text"
            placeholder="Digite o nome do grupo"
            label="Nome do grupo"
            error={!!errors.name}
            feedbackText={errors.name?.message}
            onChangeValue={field.onChange}
          />
        )}
      />
      <Controller
        control={control}
        name="description"
        render={({ field }) => (
          <InputTextBox
            id="description"
            type="text"
            placeholder="Digite a descrição do grupo"
            label="Descrição"
            error={!!errors.description}
            feedbackText={errors.description?.message}
            onChangeValue={field.onChange}
          />
        )}
      />
      <SC.CheckboxWrapper>
        <Controller
          control={control}
          name="requireCompany"
          render={({ field }) => (
            <Checkbox.Container>
              <Checkbox
                id="requireCompany"
                onChange={field.onChange}
              />
              <Checkbox.Label htmlFor="requireCompany">
                Requer empresa
              </Checkbox.Label>
            </Checkbox.Container>
          )}
        />
      </SC.CheckboxWrapper>

      {/* <div>
        <Text as="h2">Permissões</Text>

        <SC.CheckboxWrapper>
          <Controller
            control={control}
            name="permission.start"
            render={({ field }) => (
              <Checkbox.Container>
                <Checkbox
                  id="start"
                  onChange={field.onChange}
                />
                <Checkbox.Label htmlFor="start">Início</Checkbox.Label>
              </Checkbox.Container>
            )}
          />

          <Controller
            control={control}
            name="permission.app"
            render={({ field }) => (
              <Checkbox.Container>
                <Checkbox
                  id="appermission.app"
                  onChange={field.onChange}
                />
                <Checkbox.Label htmlFor="appermission.app">Apps</Checkbox.Label>
              </Checkbox.Container>
            )}
          />
          <Controller
            control={control}
            name="permission.indicators"
            render={({ field }) => (
              <Checkbox.Container>
                <Checkbox
                  id="inpermission.indicators"
                  onChange={field.onChange}
                />
                <Checkbox.Label htmlFor="inpermission.indicators">
                  Indicadores
                </Checkbox.Label>
              </Checkbox.Container>
            )}
          />
          <Controller
            control={control}
            name="permission.faq"
            render={({ field }) => (
              <Checkbox.Container>
                <Checkbox
                  id="fpermission.faq"
                  onChange={field.onChange}
                />
                <Checkbox.Label htmlFor="fpermission.faq">FAQ</Checkbox.Label>
              </Checkbox.Container>
            )}
          />
          <Controller
            control={control}
            name="permission.system"
            render={({ field }) => (
              <Checkbox.Container>
                <Checkbox
                  id="system"
                  onChange={field.onChange}
                />
                <Checkbox.Label htmlFor="system">Sistema</Checkbox.Label>
              </Checkbox.Container>
            )}
          />
        </SC.CheckboxWrapper>
      </div> */}

      <SC.WrapperButtons>
        <Button
          buttonType="secondary"
          variant="secondary"
          onClick={onClose}
          type="button"
        >
          Cancelar
        </Button>
        <Button
          variant="secondary"
          type="submit"
          color="white"
        >
          Cadastrar
        </Button>
      </SC.WrapperButtons>
    </SC.Container>
  );
}
