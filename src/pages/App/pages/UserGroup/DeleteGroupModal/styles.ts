import { Dialog } from '@onyma-ds/react';
import styled from 'styled-components';

export const Content = styled(Dialog.Content)`
  max-width: min(480px, 100vw);
`;

export const Trigger = styled.button`
  color: ${({ theme }) => theme.colors.white};
  background-color: ${({ theme }) => theme.colors.primary};
  border: 1px solid ${({ theme }) => theme.colors.primary};
  font-size: 0.75rem;
  padding: 4px 8px;
  border-radius: 9999px;
  transition: filter 0.2s;

  &:hover {
    filter: brightness(0.9);
  }
`;
