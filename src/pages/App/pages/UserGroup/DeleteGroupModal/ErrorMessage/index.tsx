import { Button } from '@onyma-ds/react';

import * as SC from './styles';

export default function DeleteUserErrorMessage({
  onClose,
}: {
  onClose: () => void;
}) {
  return (
    <SC.Container>
      <span>
        Não é possível excluir o grupo pois ele está associado a um ou mais
        usuários atualmente.
      </span>
      <SC.WrapperButtons>
        <Button
          variant="secondary"
          type="submit"
          color="white"
          onClick={onClose}
        >
          Ok, entendi!
        </Button>
      </SC.WrapperButtons>
    </SC.Container>
  );
}
