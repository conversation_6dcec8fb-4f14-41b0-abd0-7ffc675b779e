import { Heading, Text } from '@onyma-ds/react';
import * as SC from './styles';
import { Props } from './types';

const profileTypes = {
  'bencorp-internal': 'Perfil interno BenCorp',
  company: 'Perfil de empresa',
};

export default function Card({
  title,
  menuQuantity,
  menuTotal,
  profileType,
  link,
  children,
}: Props) {
  return (
    <SC.Container>
      {children}
      <Heading
        as="h4"
        type="heading_04"
      >
        {title}
      </Heading>
      <SC.Description>
        <Text type="body_03">
          possui {menuQuantity}/{menuTotal} menus
        </Text>
        <Text type="body_03">{profileTypes[profileType]}</Text>
      </SC.Description>
      <SC.ActionLink to={link}>Gerenciar perfil</SC.ActionLink>
    </SC.Container>
  );
}
