import { Icons } from '@/components';
import { PageHeader } from '../../components';
import * as SC from './styles';
import { Card } from './components';

export default function UserProfilesPage() {
  return (
    <SC.Container>
      <PageHeader.Root>
        <PageHeader.TitleBox>
          <Icons.RAFa6.FaFolderOpen size={24} />
          <PageHeader.Title>Perfis de usuário</PageHeader.Title>
        </PageHeader.TitleBox>
        <PageHeader.BackButton />
      </PageHeader.Root>
      <SC.Cards>
        <SC.CardItem>
          <Card
            title="Administrador BenCorp"
            menuQuantity={24}
            menuTotal={513}
            profileType="bencorp-internal"
            link="/app/perfis-usuarios/id"
          >
            <Icons.RAFa6.FaUserTie size={32} />
          </Card>
        </SC.CardItem>
        <SC.CardItem>
          <Card
            title="Administrador Sistema"
            menuQuantity={9}
            menuTotal={513}
            profileType="bencorp-internal"
            link="/app/perfis-usuarios/id"
          >
            <Icons.RAFa6.FaUserShield size={32} />
          </Card>
        </SC.CardItem>
        <SC.CardItem>
          <Card
            title="Ben+Saúde"
            menuQuantity={6}
            menuTotal={513}
            profileType="bencorp-internal"
            link="/app/perfis-usuarios/id"
          >
            <Icons.RAFa.FaHeartbeat size={32} />
          </Card>
        </SC.CardItem>
        <SC.CardItem>
          <Card
            title="Ben+Saúde Enfermagem"
            menuQuantity={0}
            menuTotal={513}
            profileType="bencorp-internal"
            link="/app/perfis-usuarios/id"
          >
            <Icons.RAFa.FaHeartbeat size={32} />
          </Card>
        </SC.CardItem>
        <SC.CardItem>
          <Card
            title="Ben+Saúde Médico"
            menuQuantity={0}
            menuTotal={513}
            profileType="bencorp-internal"
            link="/app/perfis-usuarios/id"
          >
            <Icons.RAFa.FaHeartbeat size={32} />
          </Card>
        </SC.CardItem>
        <SC.CardItem>
          <Card
            title="Ben+Saúde Psicólogo"
            menuQuantity={0}
            menuTotal={513}
            profileType="bencorp-internal"
            link="/app/perfis-usuarios/id"
          >
            <Icons.RAFa.FaHeartbeat size={32} />
          </Card>
        </SC.CardItem>
        <SC.CardItem>
          <Card
            title="Médico Cliente"
            menuQuantity={228}
            menuTotal={513}
            profileType="company"
            link="/app/perfis-usuarios/id"
          >
            <Icons.RAFa6.FaUserDoctor size={32} />
          </Card>
        </SC.CardItem>
        <SC.CardItem>
          <Card
            title="NVSTEC Interno"
            menuQuantity={7}
            menuTotal={513}
            profileType="company"
            link="/app/perfis-usuarios/id"
          >
            <Icons.RAFa6.FaHeart size={32} />
          </Card>
        </SC.CardItem>
        <SC.CardItem>
          <Card
            title="RH BenCorp"
            menuQuantity={63}
            menuTotal={513}
            profileType="bencorp-internal"
            link="/app/perfis-usuarios/id"
          >
            <Icons.RAFa.FaUsers size={32} />
          </Card>
        </SC.CardItem>
        <SC.CardItem>
          <Card
            title="RH Cliente"
            menuQuantity={456}
            menuTotal={513}
            profileType="company"
            link="/app/perfis-usuarios/id"
          >
            <Icons.RAFa.FaStarOfLife size={32} />
          </Card>
        </SC.CardItem>
        <SC.CardItem>
          <Card
            title="Telemedicina"
            menuQuantity={5}
            menuTotal={513}
            profileType="bencorp-internal"
            link="/app/perfis-usuarios/id"
          >
            <Icons.RAFa.FaLaptopMedical size={32} />
          </Card>
        </SC.CardItem>
      </SC.Cards>
    </SC.Container>
  );
}
