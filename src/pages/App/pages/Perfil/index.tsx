import { Icons } from '@/components';
import { PageHeader } from '../../components';
import { ChangePassword, PersonalData } from './components';
import * as SC from './styles';

export default function PerfilPage() {
  return (
    <SC.Container>
      <PageHeader.Root>
        <PageHeader.TitleBox>
          <Icons.RAFa6.FaFolderOpen size={24} />
          <PageHeader.Title>Perfil</PageHeader.Title>
        </PageHeader.TitleBox>
        <PageHeader.BackButton />
      </PageHeader.Root>
      <SC.Content>
        <PersonalData />
        <ChangePassword />
      </SC.Content>
    </SC.Container>
  );
}
