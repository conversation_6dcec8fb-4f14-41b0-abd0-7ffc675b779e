import { InputTextBox } from '@onyma-ds/react';
import { useAuth } from '@/contexts/auth';
import * as SC from './styles';

export default function PersonalFields() {
  const { user } = useAuth();
  return (
    <SC.Container>
      <InputTextBox
        label="Nome"
        defaultValue={user.fullName}
        readOnly
      />
      <InputTextBox
        label="E-mail"
        defaultValue={user.email}
        readOnly
      />
      <InputTextBox
        label="Empresa"
        defaultValue={user.companyName || 'Sem empresa'}
        readOnly
      />
    </SC.Container>
  );
}
