import React, { useState } from 'react';
import * as SC from './styles';
import { Heading } from '@onyma-ds/react';
import { Icons } from '@/components';

export default function ImageField() {
  const [file, setFile] = useState<File | null>(null);
  const [imgUrl, setImgUrl] = useState<string | null>(null);

  const handleInput = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { files } = event.target;
    const fileData = files?.[0] || null;
    setFile(fileData || null);
    const reader = new FileReader();
    reader.onload = (eventReader) => {
      setImgUrl(eventReader.target?.result?.toString() || null);
    };
    if (fileData) {
      reader.readAsDataURL(fileData);
    }
  };

  return (
    <SC.Container>
      <SC.ImgPreview>
        {imgUrl && (
          <img
            src={imgUrl}
            alt={file?.name}
          />
        )}
        {!imgUrl && <Icons.RAFa6.FaUser size={80} />}
      </SC.ImgPreview>
      <SC.ImgInputBox>
        <Heading
          as="h6"
          type="heading_06"
        >
          Imagem de Perfil (PNG ou JPG)
        </Heading>
        <input
          type="file"
          name=""
          id="input-file-avatar"
          onChange={handleInput}
        />
        <SC.ImgInput>
          <label>{file ? file.name : 'Nenhum arquivo selecionado'}</label>
          <SC.ButtonPick
            as="label"
            htmlFor="input-file-avatar"
          >
            Selecionar
          </SC.ButtonPick>
        </SC.ImgInput>
        <SC.SaveButton type="button">Salvar</SC.SaveButton>
      </SC.ImgInputBox>
    </SC.Container>
  );
}
