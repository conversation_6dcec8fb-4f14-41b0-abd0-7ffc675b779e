import styled from 'styled-components';

export const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;

  @media (min-width: ${({ theme }) => `${theme.breakpoints.sm}px`}) {
    flex-direction: row;
    align-items: center;
  }
`;

export const ImgPreview = styled.div`
  width: 100px;
  height: 100px;
  border: 1px solid ${({ theme }) => theme.colors.gray_90};
  display: flex;
  justify-content: center;
  align-items: center;

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
`;

export const ImgInputBox = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;

  & > input {
    width: 0;
    height: 0;
    opacity: 0;
    visibility: hidden;
    inset: 0;
    width: 100%;
  }
`;

export const ImgInput = styled.div`
  border: 1px solid ${({ theme }) => theme.colors.gray_90};
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  overflow: hidden;
  display: grid;
  grid-template-columns: 1fr auto;
  align-items: center;
  gap: 0.5rem;

  label {
    font-size: 0.75rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
`;

export const ButtonPick = styled.button`
  font-size: 0.75rem;
  color: ${({ theme }) => theme.colors.white};
  background-color: ${({ theme }) => theme.colors.secondary};
  border: 1px solid ${({ theme }) => theme.colors.secondary};
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  cursor: pointer;

  &:hover {
    filter: brightness(0.9);
  }
`;

export const ImgInputNameSection = styled.section`
  background-color: ${({ theme }) => theme.colors.white};
`;

export const SaveButton = styled.button`
  color: ${({ theme }) => theme.colors.white};
  background-color: ${({ theme }) => theme.colors.secondary};
  border: 1px solid ${({ theme }) => theme.colors.secondary};
  border-radius: 4px;
  padding: 4px 1rem;
  transition: filter 0.2s;

  &:hover {
    filter: brightness(0.9);
  }
`;
