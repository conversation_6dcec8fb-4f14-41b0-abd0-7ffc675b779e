import React, { useState } from 'react';
import { But<PERSON>, Heading, InputTextBox, Text, useToast } from '@onyma-ds/react';
import { useApi } from '@/contexts/api';
import { useAuth } from '@/contexts/auth';
import { Card } from '..';
import { validateForm } from './validateForm';
import * as SC from './styles';
import { Form } from './types';
import { Spinner } from '@/components';

const initialForm: Form = {
  currentPassword: '',
  password: '',
  passwordRepeat: '',
  checkCurrent: true,
};

export default function ChangePassword() {
  const { addToast } = useToast();
  const { auth } = useApi();
  const { user } = useAuth();

  const [form, setForm] = useState<Form>(initialForm);
  const [errors, setErrors] = useState<Partial<Form>>({});
  const [loading, setLoading] = useState(false);

  const handleFieldChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;
    setErrors((prev) => ({ ...prev, [name]: '' }));
    setForm((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    const validation = validateForm(form);
    if (!validation.isValid) {
      return setErrors(validation.errors);
    }

    try {
      setLoading(true);
      const changePasswordResult = await auth.changePassword({
        userId: user?.uuid as string,
        currentPassword: form.currentPassword,
        password: form.password,
        passwordRepeat: form.passwordRepeat,
        checkCurrent: form.checkCurrent,
      });
      addToast({
        type: 'success',
        title: changePasswordResult.title,
        description: changePasswordResult.message,
      });
    } catch (error) {
      const errorTitle = error.response?.data.title || 'Alteração de senha';
      const description =
        error.response?.data.message || 'Erro ao alterar a senha';
      setErrors((prev) => ({
        ...prev,
        passwordRepeat: description,
      }));
      addToast({
        type: 'error',
        title: errorTitle,
        description,
        timeout: 5000,
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card title="Alterar Senha">
      <SC.Container>
        <SC.BadgeWarning>
          <Heading
            as="h6"
            type="heading_06"
          >
            Atenção
          </Heading>
          <Text>
            Anote a nova senha, após clicar em alterar sua senha será trocada.
          </Text>
        </SC.BadgeWarning>
        <SC.Fields onSubmit={handleSubmit}>
          <InputTextBox
            label="Usuário"
            type="email"
            defaultValue={user.email}
            readOnly
          />
          <InputTextBox
            label="Senha Atual"
            type="password"
            name="currentPassword"
            placeholder="Digite a senha atual"
            value={form.currentPassword}
            onChange={handleFieldChange}
            error={!!errors.checkCurrent}
            feedbackText={errors.currentPassword}
          />
          <InputTextBox
            label="Nova Senha"
            type="password"
            name="password"
            placeholder="Digite a nova senha"
            value={form.password}
            onChange={handleFieldChange}
            error={!!errors.password}
            feedbackText={errors.password}
          />
          <InputTextBox
            label="Repetir Nova Senha"
            type="password"
            name="passwordRepeat"
            placeholder="Digite a nova senha novamente"
            value={form.passwordRepeat}
            onChange={handleFieldChange}
            error={!!errors.passwordRepeat}
            feedbackText={errors.passwordRepeat}
          />
          <Button
            type="submit"
            variant="secondary"
            color="white"
          >
            {loading ? (
              <Spinner
                size={16}
                color="white"
              />
            ) : (
              'Enviar'
            )}
          </Button>
        </SC.Fields>
      </SC.Container>
    </Card>
  );
}
