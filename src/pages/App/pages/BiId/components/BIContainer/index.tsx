/* eslint-disable react-hooks/exhaustive-deps */

import { Spinner } from '@/components';
import { useApi } from '@/contexts/api';
import { useToast } from '@onyma-ds/react';
import { Embed, models } from 'powerbi-client';
import { PowerBIEmbed } from 'powerbi-client-react';
import { useEffect, useState } from 'react';
import * as SC from './styles';
import { PowerbiParams, Props } from './types';

export default function BIContainer({
  powerBiId,
  powerBiNome,
  idEmpresaCliente,
}: Props) {
  const { addToast } = useToast();
  const { bi } = useApi();

  const [powerbiParams, setPowerbiParams] = useState<PowerbiParams>({});
  const [loading, setLoading] = useState(true);

  const loadData = async () => {
    try {
      const powerBiParamsResult = await bi.loadPowerBIParams({
        biId: powerBiId as string,
      });
      setPowerbiParams(powerBiParamsResult);
    } catch (error) {
      addToast({
        type: 'error',
        title: error.response?.data?.title,
        description: error.response?.data?.message,
        timeout: 5000,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleGetEmbeddedComponent = (embeddedReport: Embed) => {
    // @ts-expect-error type
    window.report = embeddedReport as Embed;
  };

  useEffect(() => {
    if (powerBiId && powerBiNome) {
      loadData();
    }
  }, [powerBiId, powerBiNome]);

  if (loading) {
    return (
      <SC.Container>
        <SC.PowerBIWrapper>
          <Spinner
            size={32}
            color="secondary"
          />
        </SC.PowerBIWrapper>
      </SC.Container>
    );
  }

  if (
    !powerBiId ||
    !powerBiNome ||
    Object.values(powerbiParams).some((value) => !value)
  ) {
    return <SC.Container />;
  }

  return (
    <SC.Container>
      <SC.PowerBIWrapper>
        <PowerBIEmbed
          embedConfig={
            {
              type: 'report',
              accessToken: powerbiParams.accessToken,
              id: powerBiId,
              embedUrl: powerbiParams.embedUrl,
              pageName: powerBiNome,
              tokenType: models.TokenType.Embed,
              permissions: models.Permissions.All,
              pageView: 'actualSize',
              settings: {
                zoomLevel: 1,
                filterPaneEnabled: false,
                navContentPaneEnabled: false,
                panes: {
                  filters: {
                    expanded: false,
                    visible: false,
                  },
                },
              },
              ...(idEmpresaCliente
                ? {
                    filters: [
                      {
                        $schema: 'http://powerbi.com/product/schema#basic',
                        target: {
                          table: 'DimEmpresaCliente',
                          column: 'id_empresacliente_portal',
                        },
                        operator: 'In',
                        values: [idEmpresaCliente],
                      },
                    ],
                  }
                : {}),
            } as models.IReportEmbedConfiguration
          }
          eventHandlers={
            new Map([
              [
                'loaded',
                function () {
                  console.log('Report loaded');
                },
              ],
              [
                'rendered',
                function () {
                  console.log('Report rendered');
                },
              ],
              // [
              //   'error',
              //   function (event) {
              //     // console.log(event.detail);
              //   },
              // ],
              ['visualClicked', () => console.log('visual clicked')],
              ['pageChanged', (event) => console.log(event)],
            ])
          }
          cssClassName={'reportClass'}
          getEmbeddedComponent={handleGetEmbeddedComponent}
        />
      </SC.PowerBIWrapper>
    </SC.Container>
  );
}
