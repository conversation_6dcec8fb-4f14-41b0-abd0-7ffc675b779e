import { I<PERSON><PERSON>, Spinner } from '@/components';
import { useApi } from '@/contexts/api';
import { useCancelAppointment } from '@/hooks';
import { Appointment } from '@/services/api/calendar/remoteLoadAppointments';
import { Table } from '@onyma-ds/react';
import { useQuery } from '@tanstack/react-query';
import { useSearchParams } from 'react-router-dom';
import * as SC from './styles';

export default function AppointmentsTable() {
  const [searchParams] = useSearchParams();
  const {
    calendar: { loadAppointments },
  } = useApi();

  const startDateParam = searchParams.get('dataInicial');
  const endDateParam = searchParams.get('dataFinal');
  const calendarParam = searchParams.get('codigoUsuarioAgenda');
  const empresaCodigoSoc = searchParams.get('empresa');
  const matriculaFuncionarioBusca = searchParams.get('matricula');

  const { isPending, isError, data, error, isLoading } = useQuery({
    queryKey: [
      'appointments',
      calendarParam,
      endDateParam,
      startDateParam,
      empresaCodigoSoc,
      matriculaFuncionarioBusca,
    ],
    queryFn: () =>
      loadAppointments({
        dataInicial: startDateParam ? new Date(startDateParam) : new Date(),
        dataFinal: endDateParam ? new Date(endDateParam) : new Date(),
        codigoUsuarioAgenda: calendarParam ?? '',
        codigoEmpresaBusca: String(empresaCodigoSoc),
        matriculaFuncionarioBusca: String(matriculaFuncionarioBusca),
      }),
  });

  const cancelAppointmentMutation = useCancelAppointment();

  if (isPending || isLoading) {
    return (
      <SC.Centered>
        <Spinner
          color="secondary"
          size={32}
        />
      </SC.Centered>
    );
  }

  if (isError) {
    return <SC.CenteredText $color="danger">{error.message}</SC.CenteredText>;
  }

  if (data.result.length === 0 || typeof data.result === 'string') {
    return <SC.CenteredText>Nenhum agendamento encontrado</SC.CenteredText>;
  }

  const handleCancelAppointment = (appointment: Appointment) => {
    cancelAppointmentMutation.mutate({
      codigoAgendamento: appointment.codigoAgendamento,
      codigoUsuarioAgenda: appointment.codigoUsuarioAgenda,
    });
  };

  return (
    <SC.AppointmentsTable>
      <Table.THead>
        <Table.Tr>
          <Table.Th>Empresa</Table.Th>
          <Table.Th>Funcionário</Table.Th>
          <Table.Th>Agenda</Table.Th>
          <Table.Th>Tipo</Table.Th>
          <Table.Th>Data e hora</Table.Th>
          <Table.Th />
        </Table.Tr>
      </Table.THead>
      <Table.TBody>
        {data.result.map((appointment) => (
          <Table.Tr key={appointment.codigoAgendamento}>
            <Table.Td>{appointment.nomeEmpresa}</Table.Td>
            <Table.Td>{appointment.nomeFuncionario}</Table.Td>
            <Table.Td>{appointment.nomeAgenda}</Table.Td>
            <Table.Td>{appointment.nomeTipoCompromisso}</Table.Td>
            <Table.Td>
              {appointment.dataCompromisso} às {appointment.horaInicio}
            </Table.Td>
            <Table.Td>
              <SC.SquareButton
                type="button"
                title="Cancelar consulta"
                variant="danger"
                onClick={() => handleCancelAppointment(appointment)}
              >
                {cancelAppointmentMutation.variables?.codigoAgendamento ===
                  appointment.codigoAgendamento &&
                cancelAppointmentMutation.isPending ? (
                  <Spinner
                    color="white"
                    size={16}
                  />
                ) : (
                  <Icons.Symbol
                    name="delete"
                    size={16}
                  />
                )}
              </SC.SquareButton>
            </Table.Td>
          </Table.Tr>
        ))}
      </Table.TBody>
    </SC.AppointmentsTable>
  );
}
