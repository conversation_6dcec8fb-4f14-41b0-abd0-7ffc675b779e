import { I<PERSON><PERSON>, Spinner } from '@/components';
import { useApi } from '@/contexts/api';
import { useAuth } from '@/contexts/auth';
import { PageHeader } from '@/pages/App/components';
import { Text } from '@onyma-ds/react';
import { useQuery } from '@tanstack/react-query';
import { useSearchParams } from 'react-router-dom';
import { AppointmentsTable, SearchAppointmentsForm } from './components';
import * as SC from './styles';
import { useCompany } from '@/contexts/company';

export default function CancelarConsultaPage() {
  const { company } = useCompany();
  const { user } = useAuth();

  const {
    calendar: { loadCalendars },
  } = useApi();

  const [searchParams] = useSearchParams();
  const startDateParam = searchParams.get('dataInicial');
  const endDateParam = searchParams.get('dataFinal');
  const calendarParam = searchParams.get('codigoUsuarioAgenda');

  const {
    data: calendars,
    isPending,
    isError,
    error,
  } = useQuery({
    queryKey: ['calendars', company],
    queryFn: () => loadCalendars(company?.id),
    refetchOnWindowFocus: false,
  });

  if (isPending) {
    return <Spinner color="secondary" />;
  }

  if (isError) {
    return <Text>{error.message}</Text>;
  }

  return (
    <SC.PageMain>
      <PageHeader.Root>
        <PageHeader.TitleBox>
          <Icons.RAFa.FaFolderOpen size={24} />
          <PageHeader.Title>Cancelar consulta</PageHeader.Title>
        </PageHeader.TitleBox>
        <PageHeader.BackButton />
      </PageHeader.Root>
      {calendars.result.length === 0 ? (
        <SC.EmptySubmenu>
          <h2>Você não possui uma agenda vinculada.</h2>
          <Text>Entre em contato com o suporte para mais informações.</Text>
        </SC.EmptySubmenu>
      ) : (
        <>
          <SearchAppointmentsForm
            defaultValues={{
              registration: user.socRegister,
              startDate: startDateParam ? new Date(startDateParam) : undefined,
              endDate: endDateParam ? new Date(endDateParam) : undefined,
              calendar: calendarParam ?? undefined,
            }}
            calendarOptions={calendars.result.map((calendar) => ({
              label: calendar.nome,
              value: calendar.codigo,
            }))}
          />
          <AppointmentsTable />
        </>
      )}
    </SC.PageMain>
  );
}
