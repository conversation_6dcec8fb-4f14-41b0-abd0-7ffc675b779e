import { Icon<PERSON>, Spinner } from '@/components';
import { useApi } from '@/contexts/api';
import { useAuth } from '@/contexts/auth';
import { useCompany } from '@/contexts/company';
import { useQuery } from '@tanstack/react-query';
import { Card, PageHeader } from '../../components';
import { handleCardClick } from '../Apps/utils';
import * as SC from './styles';

export default function IndicatorsPage() {
  const { company } = useCompany();
  const { user } = useAuth();

  const {
    menus: { loadMenus },
  } = useApi();

  const {
    data: allSubMenus,
    isLoading,
    isFetching,
  } = useQuery({
    queryKey: ['allSubMenus', user, company],
    queryFn: () =>
      loadMenus({
        ativo: true,
        empresa: company?.id ?? user.companies[0]?.id,
        perfil: user?.currentRole.id,
        modulo: 'indicadores',
      }),
    refetchOnWindowFocus: false,
  });

  if (isLoading || isFetching) {
    return (
      <SC.ContainerLoading>
        <Spinner
          size={32}
          color="secondary"
        />
      </SC.ContainerLoading>
    );
  }

  return (
    <SC.Container>
      <SC.Section>
        <PageHeader.Root>
          <PageHeader.TitleBox>
            <Icons.RAFa6.FaFolderOpen size={24} />
            <PageHeader.Title>Indicadores</PageHeader.Title>
          </PageHeader.TitleBox>
          <PageHeader.BackButton />
        </PageHeader.Root>
        <SC.Cards>
          {allSubMenus?.result[0] ? (
            allSubMenus?.result[0]?.menus.map((menu) => (
              <SC.CardItem key={menu.id}>
                <Card
                  imgSrc={menu.image || '/imgs/indicador_01.jpg'}
                  imgAlt="Figura representando uma pessoa apontando para um calendário"
                  title={menu.title}
                  linkTo={handleCardClick(menu)}
                  targetBlank={Number(menu.type) === 4}
                />
              </SC.CardItem>
            ))
          ) : (
            <SC.EmptySubmenu>
              <h2>Nenhum menu cadastrado ainda.</h2>
            </SC.EmptySubmenu>
          )}
        </SC.Cards>
      </SC.Section>
    </SC.Container>
  );
}
