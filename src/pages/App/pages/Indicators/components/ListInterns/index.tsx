/* eslint-disable react-hooks/exhaustive-deps */
import { useEffect, useMemo, useState } from 'react';
import { useToast } from '@onyma-ds/react';
import { Menu } from '@/services/api/menus/remoteLoadMenus';
import { useApi } from '@/contexts/api';
import { useAuth } from '@/contexts/auth';
import { Card } from '@/pages/App/components';
import * as SC from './styles';
import { getRandomImage } from './data';
import { Spinner } from '@/components';

export default function ListInterns() {
  const { addToast } = useToast();
  const { user } = useAuth();
  const { auth } = useApi();

  const [loadingMenus, setLoadingMenus] = useState(true);
  const [menus, setMenus] = useState<Menu[]>([]);

  useEffect(() => {
    auth
      .loadProfile({ id: user.currentRole.id })
      .then((resultProfile) => {
        console.log(resultProfile);
        const reportModule = resultProfile.result.menus.find(
          (menu) => menu.module === 'RELATÓRIOS',
        );
        setMenus(reportModule?.menus || []);
      })
      .catch((error) => {
        console.error(error);
        addToast({
          type: 'error',
          title: error.response?.data?.title,
          description: error.response?.data?.message,
          timeout: 5000,
        });
      })
      .finally(() => setLoadingMenus(false));
  }, [user.currentRole]);

  const menusMapped = useMemo(() => {
    return menus.map((page) => {
      const image = getRandomImage();
      return {
        ...page,
        image: image.src,
        imageAlt: image.alt,
        linkTo: `/app/bi/${page.id}`,
      };
    });
  }, [menus]);

  if (loadingMenus) {
    return (
      <SC.ContainerSpinner>
        <Spinner
          size={32}
          color="secondary"
        />
      </SC.ContainerSpinner>
    );
  }

  return (
    <SC.Container>
      {menusMapped.map((page) => (
        <SC.CardItem key={page.id}>
          <Card
            imgSrc={page.image}
            imgAlt={page.imageAlt}
            title={page.title}
            linkTo={page.linkTo}
          />
        </SC.CardItem>
      ))}
    </SC.Container>
  );
}
