import { useCallback, useEffect, useState } from 'react';
import { useToast } from '@onyma-ds/react';
import { useApi } from '@/contexts/api';
import { useAuth } from '@/contexts/auth';
import { Menu } from '@/services/api/menus/remoteLoadMenus';

export const useLoad = () => {
  const { addToast } = useToast();
  const { menus: menusApi } = useApi();
  const { user } = useAuth();

  const [loading, setLoading] = useState(true);
  const [menus, setMenus] = useState<Menu[]>([]);

  const loadMenus = useCallback(async () => {
    try {
      if (!user.companyId) return;
      const responseMenu = await menusApi.loadMenus({
        companyId: user.companyId,
      });
      const menuReports = responseMenu.result.find(
        (menu) => menu.module === 'RELATÓRIOS',
      );
      if (!menuReports) return setMenus([]);
      setMenus(menuReports.menus);
    } catch (error) {
      addToast({
        type: 'error',
        title: error.response?.data?.title,
        description: error.response?.data?.message,
        timeout: 5000,
      });
    } finally {
      setLoading(false);
    }
  }, [menusApi, user, addToast]);

  useEffect(() => {
    Promise.all([loadMenus()]);
  }, [loadMenus]);

  return { loading, menus };
};
