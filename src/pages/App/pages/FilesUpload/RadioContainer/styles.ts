import styled from 'styled-components';

type RadioWrapperProps = {
  $isSelected: boolean;
};

export const Container = styled.section`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
`;

export const RadioGroup = styled.div`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, 1fr);

  gap: 1.5rem;
`;

export const RadioWrapper = styled.div<RadioWrapperProps>`
  width: 256px;
  height: 80px;
  background-color: white;
  border: 1px solid #ccc;
  box-shadow: 0 0 5px #0000003d;
  padding: 1rem;

  display: flex;
  align-items: center;
  gap: 0.2rem;
  cursor: pointer;
  border-radius: 0.25rem;

  border: ${({ $isSelected, theme }) =>
    $isSelected ? `1px solid ${theme.colors.secondary}` : '1px solid #ccc'};

  transform: ${({ $isSelected }) => ($isSelected ? 'scale(1.04)' : 'scale(1)')};

  &:hover {
    transform: scale(1.04);
    transition: 0.5s;
  }

  label {
    cursor: pointer;
    font-size: 0.875rem;
  }

  > div {
    width: 22px;
    height: 22px;
  }
`;
