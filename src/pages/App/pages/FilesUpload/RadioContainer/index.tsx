import { ErrorMessage } from '@/pages/Login/components/LoginForm/styles';
import { Radio } from '@onyma-ds/react';
import { Container, RadioGroup, RadioWrapper } from './styles';
import { useQuery } from '@tanstack/react-query';
import { useApi } from '@/contexts/api';
import { IFileTypes } from '../Page';
import { Spinner } from '@/components';

type RadioContainerProps = {
  selectedValue: IFileTypes | null;
  handleRadioChange: (id: IFileTypes) => void;
};

export function RadioContainer({
  selectedValue,
  handleRadioChange,
}: RadioContainerProps) {
  const {
    file: { loadFileTypes },
  } = useApi();

  const { data: allFileTypes, isLoading } = useQuery({
    queryKey: ['allFileTypes'],
    queryFn: () => loadFileTypes(),
    retry: 1,
    refetchOnWindowFocus: false,
    placeholderData: (previousData) => previousData,
  });

  if (isLoading) {
    return (
      <Spinner
        size={32}
        color="secondary"
      />
    );
  }

  return (
    <Container>
      <RadioGroup>
        {allFileTypes?.result.map((fileType) => {
          if (fileType.id === 8 || fileType.id === 9) return null;
          return (
            <RadioWrapper
              key={fileType.id}
              $isSelected={selectedValue?.id === fileType.id}
              onClick={() => handleRadioChange(fileType)}
            >
              <Radio
                id={String(fileType.id)}
                name={fileType.nome}
                value={fileType.id}
                checked={selectedValue?.id === fileType.id}
              />
              <label htmlFor={String(fileType.id)}>{fileType.nome}</label>
            </RadioWrapper>
          );
        })}
      </RadioGroup>

      {!selectedValue && (
        <ErrorMessage>* Esse campo é obrigatório.</ErrorMessage>
      )}
    </Container>
  );
}
