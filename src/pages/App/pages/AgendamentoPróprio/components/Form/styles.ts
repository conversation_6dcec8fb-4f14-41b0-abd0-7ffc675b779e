import styled from 'styled-components';

export const Container = styled.form`
  max-width: 720px;
  width: 100%;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;

  textarea {
    min-height: 90px;
  }
`;

export const ContainerFeedback = styled(Container)`
  color: ${({ theme }) => theme.colors.tertiary};
  text-align: center;
  margin-top: 4rem;
`;

export const Fieldset = styled.fieldset`
  border: none;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;

  legend {
    margin-bottom: 0.5rem;
    font-size: 1.25rem;
    font-weight: 600;
  }
`;

export const FieldsSection = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
`;

export const Footer = styled.footer`
  width: 100%;
  margin-top: 4rem;

  button {
    width: 100%;
    display: grid;
    place-items: center;
  }
`;
