import { useCallback, useEffect, useState } from 'react';
import {
  Button,
  Heading,
  InputBox,
  InputDateBox,
  InputTextBox,
  SelectBox,
  TextareaBox,
  useToast,
} from '@onyma-ds/react';
import { useAuth } from '@/contexts/auth';
import { useApi } from '@/contexts/api';
import { ComboboxHours } from '..';
import { generateHours } from './generateHours';
import { validateForm } from './validateForm';
import { useLoad } from './hooks/useLoad';
import { Spinner } from '@/components';
import { generateDates } from './generateDates';
import { Errors, Form as FormType } from './types';
import * as SC from './styles';

const formInitial: FormType = {
  company: '',
  registration: '',
  name: '',
  email: '',
  address: '',
  date1: undefined,
  date2: undefined,
  date3: undefined,
  hour1: '',
  hour2: '',
  hour3: '',
};

export default function Form() {
  const { addToast } = useToast();
  const { user } = useAuth();
  const { calendar } = useApi();

  const [form, setForm] = useState<FormType>(formInitial);
  const [errors, setErrors] = useState<Errors>({});
  const [loading, setLoading] = useState(false);

  const { messageToShow, companiesOptions } = useLoad();

  const changeFormToInitial = useCallback(() => {
    setForm((prev) => ({
      ...prev,
      company: user.companyId || '',
      registration: user.socRegister,
      name: user.fullName,
      email: user.email,
    }));
  }, [user]);

  const handleFormChange = (field: string, value: string) => {
    setErrors((prev) => ({ ...prev, [field]: '' }));
    setForm((prev) => ({ ...prev, [field]: value }));
  };

  const handleDateChange = (field: string, value: Date | undefined) => {
    setErrors((prev) => ({ ...prev, [field]: '' }));
    setForm((prev) => ({ ...prev, [field]: value }));
  };

  const handleInputChange = (
    event:
      | React.ChangeEvent<HTMLInputElement>
      | React.ChangeEvent<HTMLTextAreaElement>,
  ) => {
    const { name, value } = event.target;
    setErrors((prev) => ({ ...prev, [name]: '' }));
    setForm((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    if (loading) return;
    const validation = validateForm(form);
    if (!validation.isValid) {
      return setErrors(validation.errors);
    }

    try {
      setLoading(true);
      const data = {
        idEmpresaCliente: form.company,
        matricula: form.registration,
        nomeFuncionario: form.name,
        codigoFuncionario: form.registration,
        tipoExame: 'PERIODICO', //nome do exame agendado
        datasAgendamentoManual: [
          form.date1?.toLocaleDateString('pt-BR') + ' - ' + form.hour1,
          form.date2?.toLocaleDateString('pt-BR') + ' - ' + form.hour2,
          form.date3?.toLocaleDateString('pt-BR') + ' - ' + form.hour3,
        ],
        textoLivre: form.address, //endereço
        emailPessoal: form.email,
        email: user.email,
      };
      await calendar.manualCreateSchedule(data);
      addToast({
        type: 'success',
        title: 'Agendamento realizado com sucesso',
        description: 'Seu agendamento foi realizado com sucesso.',
        timeout: 5000,
      });
      setForm(formInitial);
    } catch (error) {
      const message = error.response?.data.message;
      setErrors((prevErrors) => ({
        ...prevErrors,
        hour3: message || error.message,
      }));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    changeFormToInitial();
  }, [changeFormToInitial]);

  if (messageToShow) {
    return (
      <SC.ContainerFeedback>
        <Heading type="heading_04">{messageToShow}</Heading>
      </SC.ContainerFeedback>
    );
  }

  return (
    <SC.Container onSubmit={handleSubmit}>
      <SelectBox
        label="Selecione uma empresa"
        placeholder="Selecione uma empresa"
        options={companiesOptions}
        optionSelected={companiesOptions.find((o) => o.value === form.company)}
        onSelect={(option) => handleFormChange('company', option?.value || '')}
        error={!!errors.company}
        feedbackText={errors.company}
      />
      <InputTextBox
        name="registration"
        placeholder="Informe a matrícula do colaborador"
        label="Matrícula do colaborador"
        value={form.registration}
        onChange={handleInputChange}
        error={!!errors.registration}
        feedbackText={errors.registration}
      />
      <InputTextBox
        name="name"
        placeholder="Informe nome do colaborador"
        label="Nome do colaborador"
        value={form.name}
        onChange={handleInputChange}
        error={!!errors.name}
        feedbackText={errors.name}
      />
      <InputTextBox
        type="email"
        placeholder="Informe e-mail do colaborador"
        name="email"
        label="E-mail pessoal"
        value={form.email}
        onChange={handleInputChange}
        error={!!errors.email}
        feedbackText={errors.email}
      />
      <TextareaBox
        label="Insira seu endereço"
        placeholder="Informe o endereço"
        name="address"
        value={form.address}
        onChange={handleInputChange}
        error={!!errors.address}
        feedbackText={errors.address}
      />
      <SC.Fieldset>
        <legend>Selecione 3 Datas e Horários</legend>
        <SC.FieldsSection>
          <InputDateBox
            label="Data 1"
            availableDates={generateDates()}
            selectedDate={form.date1}
            onSelectDate={(date) => handleDateChange('date1', date)}
            error={!!errors.date1}
            feedbackText={errors.date1}
          />
          <InputBox
            label="Hora 1"
            error={!!errors.hour1}
            feedbackText={errors.hour1}
          >
            <ComboboxHours
              data={generateHours()}
              value={form.hour1}
              onChange={(option) => handleFormChange('hour1', option)}
            />
          </InputBox>
        </SC.FieldsSection>
        <SC.FieldsSection>
          <InputDateBox
            label="Data 2"
            availableDates={generateDates()}
            selectedDate={form.date2}
            onSelectDate={(date) => handleDateChange('date2', date)}
            error={!!errors.date2}
            feedbackText={errors.date2}
          />
          <InputBox
            label="Hora 2"
            error={!!errors.hour2}
            feedbackText={errors.hour2}
          >
            <ComboboxHours
              data={generateHours()}
              value={form.hour2}
              onChange={(option) => handleFormChange('hour2', option)}
            />
          </InputBox>
        </SC.FieldsSection>
        <SC.FieldsSection>
          <InputDateBox
            label="Data 3"
            availableDates={generateDates()}
            selectedDate={form.date3}
            onSelectDate={(date) => handleDateChange('date3', date)}
            error={!!errors.date3}
            feedbackText={errors.date3}
          />
          <InputBox
            label="Hora 3"
            error={!!errors.hour3}
            feedbackText={errors.hour3}
          >
            <ComboboxHours
              data={generateHours()}
              value={form.hour3}
              onChange={(option) => handleFormChange('hour3', option)}
            />
          </InputBox>
        </SC.FieldsSection>
      </SC.Fieldset>
      <SC.Footer>
        <Button
          type="submit"
          variant="secondary"
          color="white"
        >
          {loading ? (
            <Spinner
              size={16}
              color="white"
            />
          ) : (
            'Enviar'
          )}
        </Button>
      </SC.Footer>
    </SC.Container>
  );
}
