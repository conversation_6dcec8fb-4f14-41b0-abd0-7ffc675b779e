import { z } from 'zod';

export const loginFormSchema = z.object({
  username: z
    .string()
    .min(1, 'Campo obrigatório')
    .max(50, 'O login deve ter no máximo 50 caracteres'),
  password: z
    .string()
    .min(1, 'Campo obrigatório')
    .max(100, 'A senha deve ter no máximo 100 caracteres'),
  keepSession: z.boolean().optional().default(false),
});

export type LoginFormValues = z.infer<typeof loginFormSchema>;
