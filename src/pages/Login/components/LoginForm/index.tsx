import { Link, useNavigate } from 'react-router-dom';
import { Checkbox, InputText, useToast } from '@onyma-ds/react';
import { Controller, SubmitHandler, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { purify } from '@/utils/purify';
import { useAuth } from '@/contexts/auth';
import { PasswordInput, Spinner } from '@/components';
import { loginFormSchema, LoginFormValues } from './validation';
import * as SC from './styles';

export default function LoginForm() {
  const navigate = useNavigate();
  const { addToast } = useToast();
  const { login } = useAuth();
  const {
    control,
    formState: { errors, isSubmitting },
    setError,
    clearErrors,
    handleSubmit,
  } = useForm<LoginFormValues>({
    resolver: zodResolver(loginFormSchema),
    defaultValues: {
      username: '',
      password: '',
      keepSession: false,
    },
  });

  const onSubmit: SubmitHandler<LoginFormValues> = async (data) => {
    try {
      clearErrors('root');
      const loginResult = await login({
        username: data.username,
        password: data.password,
        keepSession: data.keepSession,
      });
      addToast({
        type: 'success',
        title: loginResult.title,
        description: loginResult.message,
        timeout: 5000,
      });
      if (loginResult.result.isFirstLogin) {
        navigate('/primeiro-acesso', { replace: true });
        return;
      }
      navigate('/app', { replace: true });
    } catch (error) {
      const message = error.response?.data?.message;
      setError('root', { message });
    }
  };

  return (
    <SC.LoginForm onSubmit={handleSubmit(onSubmit)}>
      <SC.LoginHeader>
        <SC.LoginHeading>Entrar no Portal BenCorp</SC.LoginHeading>
      </SC.LoginHeader>
      <SC.LoginBody>
        <Controller
          control={control}
          name="username"
          render={({
            field: { ref, name, value, disabled, onBlur, onChange },
            fieldState: { error },
          }) => (
            <SC.Field>
              <SC.Label htmlFor="username">Login</SC.Label>
              <InputText
                id="username"
                placeholder="Informe o seu login"
                ref={ref}
                name={name}
                value={value}
                disabled={disabled}
                onBlur={onBlur}
                onChange={({ target: { value } }) =>
                  onChange(purify.sanitize(value))
                }
              />
              {error && (
                <SC.ErrorMessage role="alert">{error.message}</SC.ErrorMessage>
              )}
            </SC.Field>
          )}
        />
        <Controller
          control={control}
          name="password"
          render={({
            field: { ref, name, value, disabled, onBlur, onChange },
            fieldState: { error },
          }) => (
            <SC.Field>
              <SC.Label htmlFor="password">Senha</SC.Label>
              <PasswordInput
                id="password"
                placeholder="Informe a sua senha"
                ref={ref}
                name={name}
                value={value}
                disabled={disabled}
                onBlur={onBlur}
                onChange={({ target: { value } }) =>
                  onChange(purify.sanitize(value))
                }
              />
              {error && (
                <SC.ErrorMessage role="alert">{error.message}</SC.ErrorMessage>
              )}
            </SC.Field>
          )}
        />
        <Controller
          control={control}
          name="keepSession"
          render={({ field: { name, value, disabled, onBlur, onChange } }) => (
            <Checkbox.Container>
              <Checkbox
                id="keepSession"
                name={name}
                checked={value}
                disabled={disabled}
                onBlur={onBlur}
                onChange={onChange}
              />
              <SC.CheckboxLabel htmlFor="keepSession">
                Lembre de mim
              </SC.CheckboxLabel>
            </Checkbox.Container>
          )}
        />
        {errors.root && (
          <SC.ErrorMessage role="alert">{errors.root.message}</SC.ErrorMessage>
        )}
      </SC.LoginBody>
      <SC.LoginFooter>
        <SC.LoginButton
          type="submit"
          disabled={!!errors.username || !!errors.password || isSubmitting}
        >
          {isSubmitting ? (
            <Spinner
              size={20}
              color="white"
            />
          ) : (
            'Entrar'
          )}
        </SC.LoginButton>
        <Link to="/esqueci-minha-senha">
          <SC.ResetPasswordButton type="button">
            Esqueci minha senha
            <SC.RoundedIcon name="chevron_right" />
          </SC.ResetPasswordButton>
        </Link>
        <SC.RequestAccessText>
          ou <Link to="/solicitar-acesso">Solicitar acesso</Link>
        </SC.RequestAccessText>
      </SC.LoginFooter>
    </SC.LoginForm>
  );
}
