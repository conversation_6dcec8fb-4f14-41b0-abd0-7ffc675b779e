import styled from 'styled-components';

export const Container = styled.main``;

export const Content = styled.section`
  margin: 0 auto;
  padding: 8rem 1rem 6rem;
  display: flex;
  flex-direction: column;
  gap: 10rem;

  @media (min-width: ${({ theme }) => `${theme.breakpoints.md}px`}) {
    max-width: 720px;
  }

  @media (min-width: ${({ theme }) => `${theme.breakpoints.lg}px`}) {
    max-width: 960px;
  }

  @media (min-width: ${({ theme }) => `${theme.breakpoints.xl}px`}) {
    max-width: 1144px;
  }
`;
