import styled from 'styled-components';

export const Container = styled.nav`
  display: flex;
  justify-content: center;
  flex-direction: column;
  gap: 1rem;

  overflow: hidden;
  transition: max-height 0.2s;

  & > a {
    font-size: 0.875rem;
    text-decoration: none;
    transition: color 0.5s;

    &:hover {
      color: ${({ theme }) => theme.colors.warning_dark};
      filter: brightness(1.125);
    }
  }

  &[aria-expanded='false'] {
    max-height: 0;
  }

  &[aria-expanded='true'] {
    max-height: 150px;
  }

  @media (min-width: ${({ theme }) => `${theme.breakpoints.md}px`}) {
    flex-direction: row;
    align-items: center;
  }
`;

export const AuthButtons = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;

  & > button {
    white-space: nowrap;
  }
`;
