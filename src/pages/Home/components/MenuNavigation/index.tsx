import { But<PERSON> } from '@onyma-ds/react';
import { <PERSON> } from 'react-router-dom';
import * as SC from './styles';
import { Props } from './types';

export default function MenuNavigation(props: Props) {
  return (
    <SC.Container
      id="c-menu-navigation"
      aria-expanded={props.isOpen}
    >
      <a href="/#solucoes">Soluções</a>
      <a href="/#produtos">Produtos</a>
      <SC.AuthButtons>
        <Link
          to="/login"
          title="Fazer login"
        >
          <Button
            type="button"
            variant="secondary"
            color="white"
          >
            Entrar
          </Button>
        </Link>
        <Link
          to="/solicitar-acesso"
          title="Solicitar acesso ao sistema"
        >
          <Button
            type="button"
            variant="secondary"
            color="white"
          >
            Solicitar acesso
          </Button>
        </Link>
      </SC.AuthButtons>
    </SC.Container>
  );
}
