import styled from 'styled-components';

export const Container = styled.section`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rem;

  @media (min-width: ${({ theme }) => `${theme.breakpoints.md}px`}) {
    display: grid;
    grid-template-columns: 1fr 1fr;
    align-items: center;
    gap: 2rem;
  }
`;

export const Titles = styled.article`
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: 2rem;

  & > h4 {
    color: ${({ theme }) => theme.colors.gray_40};
  }

  & > a {
    margin-top: 2rem;
    width: min-content;
    white-space: nowrap;
  }
`;

export const ImageBox = styled.div`
  width: 100%;
  max-width: 100%;
  flex-shrink: 0;
  text-align: center;

  img {
    width: 100%;
    height: auto;
    vertical-align: middle;
  }
`;
