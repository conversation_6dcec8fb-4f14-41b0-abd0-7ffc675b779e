import { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { useWindowWidth } from '@/hooks';
import { Icons } from '@/components';
import { MenuNavigation } from '..';
import * as SC from './styles';

export default function Header() {
  const { isMobile } = useWindowWidth();

  const [menuOpen, setMenuOpen] = useState(false);

  const handleMenuOpenChange = () => setMenuOpen((prevState) => !prevState);

  useEffect(() => {
    setMenuOpen(!isMobile);
  }, [isMobile]);

  return (
    <SC.Container data-menu-state={menuOpen ? 'open' : 'closed'}>
      <Link
        to="/"
        title="Navegar para a página inicial"
      >
        <img
          src="/imgs/bencorp-logo.png"
          alt="Logo da BenCorp"
          loading="lazy"
          height={90}
        />
      </Link>
      {isMobile && (
        <SC.MenuTrigger
          type="button"
          onClick={handleMenuOpenChange}
        >
          <Icons.Symbol
            name="menu"
            size={28}
          />
        </SC.MenuTrigger>
      )}
      <MenuNavigation isOpen={menuOpen} />
    </SC.Container>
  );
}
