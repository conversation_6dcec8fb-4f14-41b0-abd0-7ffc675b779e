import styled from 'styled-components';

export const Container = styled.section`
  & > h2 {
    margin-bottom: 1rem;
  }
`;

export const ProductList = styled.ul`
  @media (min-width: ${({ theme }) => `${theme.breakpoints.md}px`}) {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 2rem;
  }
`;

export const Product = styled.li`
  list-style: none;
  display: flex;
  flex-direction: column;
  gap: 1rem;

  & > img {
    width: 100%;
    height: auto;
  }

  &:not(:last-child) {
    margin-bottom: 3.5rem;
  }
`;
