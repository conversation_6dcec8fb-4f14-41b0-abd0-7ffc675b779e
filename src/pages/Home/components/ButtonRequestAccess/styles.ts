import { Link } from 'react-router-dom';
import styled from 'styled-components';

export const Container = styled(Link)`
  font-size: 1rem;
  text-align: center;
  font-weight: 600;
  line-height: 150%;
  background-color: ${({ theme }) => theme.colors.secondary};
  color: ${({ theme }) => theme.colors.white};
  border-radius: 4px;
  padding: 1rem 2.5rem;

  &:hover {
    filter: brightness(0.9);
  }
`;
