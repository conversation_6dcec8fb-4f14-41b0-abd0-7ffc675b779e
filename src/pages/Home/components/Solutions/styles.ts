import styled from 'styled-components';

export const Container = styled.section`
  display: flex;
  flex-direction: column;
  gap: 5rem;

  @media (min-width: ${({ theme }) => `${theme.breakpoints.md}px`}) {
    gap: 8rem;
  }
`;

export const SolutionsBox = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1.75rem;

  @media (min-width: ${({ theme }) => `${theme.breakpoints.md}px`}) {
    display: grid;
    grid-template-columns: 1fr 1fr;
  }
`;

export const Solutions = styled.article`
  & > h2 {
    margin-bottom: 1.5rem;
  }

  ul {
    display: flex;
    flex-direction: column;
    gap: 1.75rem;

    li {
      list-style: none;

      h4 {
        font-weight: 700;
      }

      p {
        color: ${({ theme }) => theme.colors.gray_30};
        font-weight: 500;
      }
    }
  }
`;

export const ImageBox = styled.div`
  width: 100%;
  max-width: 100%;
  text-align: center;

  & > img {
    width: 100%;
    height: auto;
  }
`;
