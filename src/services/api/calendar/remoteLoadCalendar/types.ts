import { ApiResult } from '@/@types/ApiResult';

export type Calendar = {
  nome: string;
  dataCriado: string;
  telefone: string;
  email: string;
  codigo: string;
  id: number;
  endereco: string;
  tipoExame: string;
  tipoCompromisso: string[];
  disponibilidade: {
    [key: string]: string[];
  };
  empresaCliente: {
    id: string;
    nome: string;
  }[];
};

type Result = ApiResult<Calendar>;

export type RemoteLoadCalendar = (agendaId: number) => Promise<Result>;
