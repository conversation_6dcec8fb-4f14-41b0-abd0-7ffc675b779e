import { ApiResult } from '@/@types/ApiResult';

type Params = {
  dataAgendamento: string;
  horaInicio: string;
  codigoUsuarioAgenda: string;
  codigoFuncionario: string;
  detalhes: string;
  idEmpresaCliente: string;
  nomeAgenda: string;
  enderecoAgenda: string;
  telefoneAgenda: string;
  matricula: string;
  nomeFuncionario: string;
  tipoEmail: string;
  idTipoCompromisso: string;
  idTipoExame: string;
  textoLivre: string;
  emailPessoal: string;
  email: string;
  codigoEmpresa: string;
};

export type RemoteSOCCreateSchedule = (params: Params) => Promise<ApiResult>;
