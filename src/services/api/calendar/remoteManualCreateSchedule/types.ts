import { ApiResult } from '@/@types/ApiResult';

type Params = {
  // envio: 'manual'; //quando for email de agendamento manual
  idEmpresaCliente: string;
  // nomeAgenda: 'Lorem ipsum'; //vazio
  // enderecoAgenda: 'Avenida de todos os santos, Umabara, Curitiba - PR'; //vazio
  // telefoneAgenda: '11 99999-9999'; //vazio
  matricula: string;
  nomeFuncionario: string;
  codigoFuncionario: string;
  // tipoEmail: 'AGENDAMENTO MANUAL';
  // tipoCompromisso: 'AGENDAMENTO'; //vazio
  tipoExame: string; //nome do exame agendado
  datasAgendamentoManual: string[];
  textoLivre: string; //endereço
  emailPessoal: string;
  email: string;
};

export type RemoteManualCreateSchedule = (params: Params) => Promise<ApiResult>;
