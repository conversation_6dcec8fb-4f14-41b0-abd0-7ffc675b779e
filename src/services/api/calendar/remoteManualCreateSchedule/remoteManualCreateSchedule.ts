import { httpClient } from '@/services/httpClient';
import { RemoteManualCreateSchedule } from './types';

export const remoteManualCreateSchedule: RemoteManualCreateSchedule = async (
  params,
) => {
  const response = await httpClient.post('/agendamento/enviarEmail', {
    envio: 'manual',
    idEmpresaCliente: params.idEmpresaCliente,
    matricula: params.matricula,
    nomeFuncionario: params.nomeFuncionario,
    codigoFuncionario: params.codigoFuncionario,
    tipoEmail: 'AGENDAMENTO MANUAL',
    tipoExame: params.tipoExame, //nome do exame agendado
    datasAgendamentoManual: params.datasAgendamentoManual,
    textoLivre: params.textoLivre, //endereço
    emailPessoal: params.emailPessoal,
    email: params.email,
  });
  return response.data;
};
