/* eslint-disable @typescript-eslint/no-explicit-any */
import { httpClient } from '@/services/httpClient';
import { formatters } from '@/utils/formatters';
import { RemoteLoadBIPages } from './types';

export const remoteLoadBIPages: RemoteLoadBIPages = async () => {
  try {
    const response = await httpClient.get('/bi/pagina/fetchall');
    return {
      ...response.data,
      title: formatters.titleCase(response.data.title),
      result: response.data.result?.map((item: any) => ({
        id: item.id,
        biId: item.biId,
        powerBiName: item.powerBiNome,
        name: item.nome,
        description: item.descricao,
        biIdObject: item.biIdObject,
      })),
    };
  } catch (error) {
    throw new Error('Error on remoteLoadBIPages');
  }
};
