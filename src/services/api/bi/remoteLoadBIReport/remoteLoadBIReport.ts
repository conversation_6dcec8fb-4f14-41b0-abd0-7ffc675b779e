/* eslint-disable @typescript-eslint/no-explicit-any */
import { httpClient } from '@/services/httpClient';
import { formatters } from '@/utils/formatters';
import { RemoteLoadBIReport } from './types';

export const remoteLoadBIReport: RemoteLoadBIReport = async (params) => {
  const response = await httpClient.get('/bi/fetch', {
    params: {
      id: params.id,
    },
  });
  return {
    ...response.data,
    title: formatters.titleCase(response.data.title),
    result: {
      id: response.data.result.id,
      name: response.data.result.nome,
      description: response.data.result.descricao,
      powerBiId: response.data.result.powerBiId,
      biPage: response.data.result.biPagina.map((item: any) => ({
        id: item.id,
        biId: item.biId,
        name: item.nome,
        description: item.descricao,
        powerBiName: item.powerBiNome,
      })),
    },
  };
};
