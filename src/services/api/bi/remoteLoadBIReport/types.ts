import { ApiResult } from '@/@types/ApiResult';

type Params = {
  id: string;
};

type Result = ApiResult<BIReport>;

export type BIReport = {
  id: number;
  name: string;
  description: string;
  powerBiId: string;
  biPage: {
    id: number;
    biId: number;
    name: string;
    description: string;
    powerBiName: string | null;
  }[];
};

export type RemoteLoadBIReport = (params: Params) => Promise<Result>;
