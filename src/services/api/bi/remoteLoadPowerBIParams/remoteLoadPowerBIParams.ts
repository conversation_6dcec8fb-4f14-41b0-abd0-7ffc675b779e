import axios from 'axios';
import { remoteLoadConfig } from '../../configs/remoteLoadConfig';
import { RemoteLoadPowerBIParams } from './types';
import { KEY_MICROSOFT_ACCESS_TOKEN } from '@/services/storage/keys';

export const remoteLoadPowerBIParams: RemoteLoadPowerBIParams = async (
  params,
) => {
  const microsoftAccessToken = localStorage.getItem(KEY_MICROSOFT_ACCESS_TOKEN);
  const [resultWorkspaceId] = await Promise.all([
    remoteLoadConfig({ key: 'PowerBiWorkspaceId' }),
  ]);

  const powerBIGenerate = await axios.get(
    `https://api.powerbi.com/v1.0/myorg/groups/${resultWorkspaceId.result.PowerBiWorkspaceId}/reports/${params.biId}`,
    {
      headers: {
        Authorization: `Bearer ${microsoftAccessToken}`,
      },
    },
  );

  const powerBIGenerateToken = await axios.post(
    'https://api.powerbi.com/v1.0/myorg/GenerateToken',
    {
      datasets: [{ id: powerBIGenerate.data.datasetId }],
      reports: [{ id: params.biId }],
    },
    {
      headers: {
        Authorization: `Bearer ${microsoftAccessToken}`,
      },
    },
  );
  return {
    accessToken: powerBIGenerateToken.data.token,
    embedUrl: powerBIGenerate.data.embedUrl,
    pageName: 'ReportSection1dea67c2520bce38f54f',
  };
};
