import { ApiResult } from '@/@types/ApiResult';

type Profile = {
  idPerfil: string;
  nomePerfil: string;
};

export type User = {
  id: string;
  nome: string;
  email: string;
  empresaNome: string | null;
  empresas?: { id: string; nome: string }[];
  ativo: boolean;
  dataUltimoLogin: string | null;
  dataUltimoLoginDate: Date | null;
  dataCadastro: string;
  dataCadastroDate: Date;
  perfis: Profile[];
  codigoSoc?: string | null;
  matriculaSoc?: string | null;
};

type Result = ApiResult<User[]>;

export type RemoteLoadUsers = () => Promise<Result>;
