import { httpClient } from '@/services/httpClient';
import { formatters } from '@/utils/formatters';
import { RemoteLoadPowerBiWorkspaceId } from './types';

export const remoteLoadPowerBiWorkspaceId: RemoteLoadPowerBiWorkspaceId =
  async () => {
    const response = await httpClient.post('/configuracao/fetch', null, {
      params: {
        chave: 'PowerBiWorkspaceId',
      },
    });
    return {
      ...response.data,
      title: formatters.titleCase(response.data.title),
    };
  };
