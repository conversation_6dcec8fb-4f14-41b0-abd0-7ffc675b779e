import { httpClient } from '@/services/httpClient';
import { formatters } from '@/utils/formatters';
import { RemoteLoadConfig } from '.';

export const remoteLoadConfig: RemoteLoadConfig = async (params) => {
  const response = await httpClient.post('/configuracao/fetch', null, {
    params: {
      chave: params.key,
    },
  });
  return {
    ...response.data,
    title: formatters.titleCase(response.data.title),
  };
};
