import { httpClient } from '@/services/httpClient';
import { RemoteEditUserGroup } from './types';

export const remoteEditUserGroup: RemoteEditUserGroup = async ({
  id,
  name,
  requireCompany,
  description,
}) => {
  const response = await httpClient.post('/utilidades/perfil/update', {
    id,
    nome: name,
    requerEmpresa: requireCompany,
    descricao: description,
    icone: 'fa fa-user',
    homepage: '',
    menus: [],
  });
  return response.data;
};
