import { httpClient } from '@/services/httpClient';
import { remoteCreateFileType } from './types';

export const remoteCreateFile: remoteCreateFileType = async (params) => {
  const formData = new FormData();
  const profiles = params.perfil?.split(',');

  formData.append('arquivo', params.arquivo);

  for (const [key, value] of Object.entries(params)) {
    if (key !== 'arquivo' && key !== 'perfil') {
      formData.append(key, value as string);
    }
  }

  formData.append('arquivo', params.arquivo);
  profiles?.map((profile) => {
    formData.append('perfil', profile);
  });

  if (!params.perfil) {
    formData.delete('perfil');
  }

  const response = await httpClient.post('/arquivos/create', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });

  return response.data;
};
