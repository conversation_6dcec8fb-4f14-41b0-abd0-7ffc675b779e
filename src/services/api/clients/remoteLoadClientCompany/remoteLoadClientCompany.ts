import { httpClient } from '@/services/httpClient';
import { formatters } from '@/utils/formatters';
import { RemoteLoadClientCompany } from './types';

export const remoteLoadClientCompany: RemoteLoadClientCompany = async (
  companyId: string,
) => {
  const response = await httpClient.get('/customercompany/fetch', {
    params: {
      id: companyId,
    },
  });
  return {
    ...response.data,
    title: formatters.titleCase(response.data.title),
    result: {
      id: response.data.result.id,
      name: response.data.result.nome,
      isActive: response.data.result.ativo,
      includedBenSaude: response.data.result.incluirBenMaisSaude,
      codigoSoc: response.data.result.codigoSoc,
      banner: response.data.result.banner,
      logo: response.data.result.logo,
    },
  };
};
