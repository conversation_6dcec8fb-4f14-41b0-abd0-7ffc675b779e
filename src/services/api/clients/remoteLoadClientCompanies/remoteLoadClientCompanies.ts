/* eslint-disable @typescript-eslint/no-explicit-any */
import { httpClient } from '@/services/httpClient';
import { formatters } from '@/utils/formatters';
import { RemoteLoadClientCompanies } from './types';

const getDateType = (dateString: string) => {
  const [date, hours] = dateString.split(' ');
  const [day, month, year] = date.split('/');
  return new Date(`${year}-${month}-${day}T${hours}`);
};

export const remoteLoadClientCompanies: RemoteLoadClientCompanies =
  async () => {
    const response = await httpClient.get('/customercompany/fetchall');
    return {
      ...response.data,
      title: formatters.titleCase(response.data.title),
      result: response.data.result?.map((item: any) => ({
        id: item.id,
        name: item.nome,
        isActive: item.ativo,
        includedBenSaude: item.incluirBenMaisSaude,
        codigoSoc: item.codigoSoc,
        createdAt: getDateType(item.dataCadastro),
        createdAtFormatted: item.dataCadastro,
        logo: item.logo,
      })),
    };
  };
