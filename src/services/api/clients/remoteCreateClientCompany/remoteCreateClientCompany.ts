import { httpClient } from '@/services/httpClient';
import { RemoteCreateClientCompany } from './types';

export const remoteCreateClientCompany: RemoteCreateClientCompany = async (
  params,
) => {
  const response = await httpClient.post('/customercompany/create', {
    nome: params.name,
    inclusoBenMaisSaude: params.includedBenSaude,
    codigoSoc: params.codigoSoc,
    logo: params.logo,
  });
  return response.data;
};
