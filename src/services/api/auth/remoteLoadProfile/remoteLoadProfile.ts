/* eslint-disable @typescript-eslint/no-explicit-any */
import { httpClient } from '@/services/httpClient';
import { formatters } from '@/utils/formatters';
import { mapMenus } from '@/services/apiMappers/menus';
import { RemoteLoadProfile } from './types';

export const remoteLoadProfile: RemoteLoadProfile = async (params) => {
  const response = await httpClient.get('/utilidades/perfil/fetch', {
    params: {
      id: params.id,
    },
  });
  return {
    ...response.data,
    title: formatters.titleCase(response.data.title),
    result: {
      ...response.data.result,
      menus: response.data.result.menus.map((menu: any) => ({
        module: menu.modulo,
        menus: mapMenus(menu.menus),
      })),
    },
  };
};
