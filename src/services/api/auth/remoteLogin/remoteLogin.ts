import { httpClient } from '@/services/httpClient';
import { formatters } from '@/utils/formatters';
import { Params, Result } from './types';
import { remoteLoadConfig } from '../../configs/remoteLoadConfig';

export const remoteLogin = async (params: Params): Promise<Result> => {
  const response = await httpClient.post('/auth/user', {
    identity: params.username,
    password: params.password,
  });
  const [
    resultTenant,
    resultApplicationSecret,
    resultApplicationId,
    resultResourceUrl,
  ] = await Promise.all([
    remoteLoadConfig({ key: 'PowerBiTenant' }),
    remoteLoadConfig({ key: 'PowerBiApplicationSecret' }),
    remoteLoadConfig({ key: 'PowerBiApplicationId' }),
    remoteLoadConfig({ key: 'PowerBiResourceUrl' }),
  ]);

  const microsoftTokenResult = await httpClient.post(
    'requestToken',
    {
      tenant_id: resultTenant.result.PowerBiTenant,
      grant_type: 'client_credentials',
      client_secret: resultApplicationSecret.result.PowerBiApplicationSecret,
      client_id: resultApplicationId.result.PowerBiApplicationId,
      resource: resultResourceUrl.result.PowerBiResourceUrl,
    },
    {
      headers: {
        'Content-Type': 'application/json',
      },
    },
  );
  return {
    ...response.data,
    microsoftAccessToken: microsoftTokenResult.data.access_token,
    title: formatters.titleCase(response.data.title),
  };
};
