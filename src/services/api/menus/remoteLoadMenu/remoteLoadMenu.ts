/* eslint-disable @typescript-eslint/no-explicit-any */
import { httpClient } from '@/services/httpClient';
import { formatters } from '@/utils/formatters';
import { MenuComplete, RemoteLoadMenu } from '.';

const mapMenu = (menuData: any): MenuComplete => {
  return {
    id: menuData.id,
    name: menuData.nome,
    url: menuData.url,
    iframe: menuData.iframe,
    order: menuData.ordem,
    height: menuData.altura,
    ativo: menuData.ativo,
    submenu: menuData.submenu,
    module: menuData.modulo,
    empresas: menuData.empresas,
    perfis: menuData.perfis,
    imagem: menuData.imagem ?? '',
    menuParentId: menuData.idMenuPai,
    menuParentName: menuData.nomeMenuPai,
    menuTypeId: menuData.menuTipoId,
    agendaId: menuData.agendaId,
    menus: menuData.menus,
    bi: menuData.bi,
    tipoCompromisso: menuData.tipoCompromisso,
    biPagina: menuData.biPagina,
    description: menuData.descricao,
  };
};

export const remoteLoadMenu: RemoteLoadMenu = async (params) => {
  const response = await httpClient.get('/utilidades/menu/fetch', {
    params: {
      id: params.id,
    },
  });
  return {
    ...response.data,
    title: formatters.titleCase(response.data.title),
    result: mapMenu(response.data.result),
  };
};
