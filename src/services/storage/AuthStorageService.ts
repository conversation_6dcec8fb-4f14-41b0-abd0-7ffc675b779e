import { AuthUser } from '@/contexts/auth/types';
import {
  KEY_ACCESS_TOKEN,
  KEY_AUTH_KEEP_SESSION,
  KEY_MICROSOFT_ACCESS_TOKEN,
  KEY_REFRESH_TOKEN,
  KEY_USER,
} from './keys';

export class AuthStorageService {
  static setAccessToken(token: string) {
    localStorage.setItem(KEY_ACCESS_TOKEN, token);
  }

  static getAccessToken() {
    return localStorage.getItem(KEY_ACCESS_TOKEN);
  }

  static removeAccessToken() {
    localStorage.removeItem(KEY_ACCESS_TOKEN);
  }

  static setMicrosoftAccessToken(token: string) {
    localStorage.setItem(KEY_MICROSOFT_ACCESS_TOKEN, token);
  }

  static getMicrosoftAccessToken() {
    return localStorage.getItem(KEY_MICROSOFT_ACCESS_TOKEN);
  }

  static removeMicrosoftAccessToken() {
    localStorage.removeItem(KEY_MICROSOFT_ACCESS_TOKEN);
  }

  static setRefreshToken(token: string) {
    localStorage.setItem(KEY_REFRESH_TOKEN, token);
  }

  static getRefreshToken() {
    return localStorage.getItem(KEY_REFRESH_TOKEN);
  }

  static removeRefreshToken() {
    localStorage.removeItem(KEY_REFRESH_TOKEN);
  }

  static setAuthKeepSession(keepSession: boolean) {
    localStorage.setItem(KEY_AUTH_KEEP_SESSION, String(keepSession));
  }

  static getAuthKeepSession() {
    return localStorage.getItem(KEY_AUTH_KEEP_SESSION) === 'true';
  }

  static removeAuthKeepSession() {
    localStorage.removeItem(KEY_AUTH_KEEP_SESSION);
  }

  static getAuthTokens() {
    const accessToken = this.getAccessToken();
    const refreshToken = this.getRefreshToken();
    return { accessToken, refreshToken };
  }

  static setUser(user: AuthUser) {
    localStorage.setItem(KEY_USER, JSON.stringify(user));
  }

  static getUser() {
    const user = localStorage.getItem(KEY_USER);
    return user ? (JSON.parse(user) as AuthUser) : null;
  }

  static removeUser() {
    localStorage.removeItem(KEY_USER);
  }

  static clear() {
    this.removeAccessToken();
    this.removeMicrosoftAccessToken();
    this.removeRefreshToken();
    this.removeAuthKeepSession();
    this.removeUser();
  }
}
