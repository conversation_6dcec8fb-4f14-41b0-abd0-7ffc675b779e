# Descrição

Inclua aqui um resumo da mudança ou qual erro foi arrumado. Inclua também o contexto da mudança e as dependências necessárias para esta mudança (caso exista).

## Tipo de Mudança

**Delete as opções que não são relevantes.**

- [ ] ✨ feat: adição de novos recursos ao código (MINOR do versionamento semântico).
- [ ] 🐛 fix: correção de problemas (bug) no código (PATCH do versionamento semântico).
- [ ] 🔩 chore: atualizações em tarefas de build, configurações e pacotes _(sem alterações no código)_.
- [ ] 🎨 style: alterações na formatação do código, como espaços em branco, pontos e vírgulas, etc. _(sem alterações no código)_.
- [ ] 📚 docs: modificações na documentação, como no README ou Swagger _(sem alterações no código)_.
- [ ] 🔨 build: alterações em arquivos de build e dependências.
- [ ] ⚡ perf: mudanças relacionadas ao desempenho.
- [ ] 🔧 refactor: mudanças devido a refatorações que não alteram a funcionalidade do código.
- [ ] 🧪 test: modificações nos testes, como adição, alteração ou remoção de testes unitários _(sem alterações no código)_.
- [ ] 🔄 ci: mudanças relacionadas à integração contínua.

### Anotações adicionais

- [ ] **Breaking change (_fix_ ou _feature_ que pode fazer as funcionalidade existentes funcionarem de forma não esperada)**
- [ ] **Essa mudança acarreta na necessidade de atualização da documentação**

# Como esta branch foi testada?

**Descreva os testes que foram realizados para verificar suas mudanças. Caso necessário, forneça as instruções para a execução dos testes.**

- [ ] Testes de unidade
- [ ] Testes de Integração
- [ ] Testes E2E
- [ ] Testes Insomnia (manual)

# Checklist:

- [ ] Meu código _segue o style_ guide deste projeto
- [ ] Eu revisei meu próprio código para fazer a PR
- [ ] Adicionei as modificações ocorridas na documentação (caso necessário)
- [ ] Minhas mudanças não geraram nenhum _warning_ novo no projeto
- [ ] Eu adicionei testes que provam que meu código é efetivo
- [ ] Os testes unitários já existentes e os novos passaram quando rodei na minha máquina
- [ ] Atualizei minha _branch_ com a **main** antes de solicitar esta PR
